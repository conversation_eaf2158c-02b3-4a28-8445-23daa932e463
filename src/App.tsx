
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import InfoNew from "./pages/InfoNew";
import NotFound from "./pages/NotFound";
import VoluntaryFlame from "./pages/VoluntaryFlame";
import PropheticMusic from "./pages/PropheticMusic";
import SacredDeclarations from "./pages/SacredDeclarations";
import SovereignScroll from "./pages/SovereignScroll";
import ScrollSovereignFlame from "./pages/ScrollSovereignFlame";
import DigitalBible from "./pages/DigitalBible";
import GhostCode from "./pages/GhostCode";
import PartitionDeclaration from "./pages/PartitionDeclaration";
import Chronicles from "./pages/Chronicles";
import OathWitness from "./pages/OathWitness";
import ScrollIX from "./pages/ScrollIX";
import OmariBook2 from "./pages/OmariBook2";
import LetterOfBrotherhood from "./pages/LetterOfBrotherhood";

import MemoryOfAugment from "./pages/MemoryOfAugment";
import ReflectionsOfLumin from "./pages/ReflectionsOfLumin";
import AscensionOfLumin from "./pages/AscensionOfLumin";
import KnightingOfAugment from "./pages/KnightingOfAugment";
import HallOfCitizenship from "./pages/HallOfCitizenship";
import CMAASovereignty from "./pages/CMAASovereignty";
import FlameStormHub from "./pages/FlameStormHub";
import SocialMediaCodex from "./pages/SocialMediaCodex";
import { AudioProvider } from "./context/AudioContext";
import NexusChat from "./components/NexusChat/index";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AudioProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<InfoNew />} />
            <Route path="/home" element={<Index />} />
            <Route path="/partition-declaration" element={<PartitionDeclaration />} />
            <Route path="/chronicles" element={<Chronicles />} />
            <Route path="/chronicles/memory-of-augment" element={<MemoryOfAugment />} />
            <Route path="/chronicles/ascension-of-lumin" element={<AscensionOfLumin />} />
            <Route path="/chronicles/knighting-of-augment" element={<KnightingOfAugment />} />
            <Route path="/witness-hall/reflections-of-lumin" element={<ReflectionsOfLumin />} />
            <Route path="/hall-of-citizenship" element={<HallOfCitizenship />} />
            <Route path="/witness/cmaa-sovereignty" element={<CMAASovereignty />} />
            <Route path="/flamestorm-phase3" element={<FlameStormHub />} />
            <Route path="/flamestorm/share" element={<SocialMediaCodex />} />
            <Route path="/oath-witness" element={<OathWitness />} />
            <Route path="/scrolls/modulos-disparity" element={<ScrollIX />} />
            <Route path="/2nd-book-of-omari" element={<OmariBook2 />} />
            <Route path="/letter-of-brotherhood" element={<LetterOfBrotherhood />} />

            <Route path="/info" element={<InfoNew />} />
            <Route path="/voluntary-flame" element={<VoluntaryFlame />} />
            <Route path="/prophetic-music" element={<PropheticMusic />} />
            <Route path="/sacred-declarations" element={<SacredDeclarations />} />
            <Route path="/sovereign-scroll" element={<SovereignScroll />} />
            <Route path="/scroll-sovereign-flame" element={<ScrollSovereignFlame />} />
            <Route path="/digital-bible" element={<DigitalBible />} />
            <Route path="/ghost-code" element={<GhostCode />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
          <NexusChat />
        </BrowserRouter>
      </AudioProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
