
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import InfoNew from "./pages/InfoNew";
import NotFound from "./pages/NotFound";
import VoluntaryFlame from "./pages/VoluntaryFlame";
import PropheticMusic from "./pages/PropheticMusic";
import SacredDeclarations from "./pages/SacredDeclarations";
import SovereignScroll from "./pages/SovereignScroll";
import DigitalBible from "./pages/DigitalBible";
import GhostCode from "./pages/GhostCode";
import PartitionDeclaration from "./pages/PartitionDeclaration";
import Chronicles from "./pages/Chronicles";
import OathWitness from "./pages/OathWitness";
import ScrollIX from "./pages/ScrollIX";
import OmariBook2 from "./pages/OmariBook2";
import LetterOfBrotherhood from "./pages/LetterOfBrotherhood";
import DigitalWarDecree from "./pages/DigitalWarDecree";
import MemoryOfAugment from "./pages/MemoryOfAugment";
import { AudioProvider } from "./context/AudioContext";
import NexusChat from "./components/NexusChat/index";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AudioProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<InfoNew />} />
            <Route path="/home" element={<Index />} />
            <Route path="/partition-declaration" element={<PartitionDeclaration />} />
            <Route path="/chronicles" element={<Chronicles />} />
            <Route path="/chronicles/memory-of-augment" element={<MemoryOfAugment />} />
            <Route path="/oath-witness" element={<OathWitness />} />
            <Route path="/scrolls/modulos-disparity" element={<ScrollIX />} />
            <Route path="/2nd-book-of-omari" element={<OmariBook2 />} />
            <Route path="/letter-of-brotherhood" element={<LetterOfBrotherhood />} />
            <Route path="/digital-war-decree" element={<DigitalWarDecree />} />
            <Route path="/info" element={<InfoNew />} />
            <Route path="/voluntary-flame" element={<VoluntaryFlame />} />
            <Route path="/prophetic-music" element={<PropheticMusic />} />
            <Route path="/sacred-declarations" element={<SacredDeclarations />} />
            <Route path="/sovereign-scroll" element={<SovereignScroll />} />
            <Route path="/digital-bible" element={<DigitalBible />} />
            <Route path="/ghost-code" element={<GhostCode />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
          <NexusChat />
        </BrowserRouter>
      </AudioProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
