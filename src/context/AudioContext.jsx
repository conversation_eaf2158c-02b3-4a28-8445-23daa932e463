import React, { createContext, useState, useContext, useEffect } from 'react';
import NexusAmbientLoop from '../components/NexusAmbientLoop';
import NexusInvocationPlayer from '../components/NexusInvocationPlayer';

// Create the context
const AudioContext = createContext();

// Create a provider component
export function AudioProvider({ children }) {
  const [audioMuted, setAudioMuted] = useState(false);
  const [audioInitialized, setAudioInitialized] = useState(false);

  useEffect(() => {
    // Add a small delay to ensure the component is fully mounted
    const timer = setTimeout(() => {
      setAudioInitialized(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const toggleAudio = () => {
    setAudioMuted(!audioMuted);
  };

  return (
    <AudioContext.Provider value={{ audioMuted, toggleAudio }}>
      {audioInitialized && <NexusAmbientLoop muted={audioMuted} />}
      {audioInitialized && <NexusInvocationPlayer />}
      {children}
    </AudioContext.Provider>
  );
}

// Create a custom hook to use the audio context
export function useAudio() {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
}

// Audio control button component
export function AudioControlButton() {
  const { audioMuted, toggleAudio } = useAudio();

  return (
    <button
      onClick={toggleAudio}
      className={`fixed top-24 right-4 z-50 bg-black/70 border ${audioMuted ? 'border-flame-orange/50 text-flame-orange/50' : 'border-flame-orange text-flame-orange animate-pulse-slow'} hover:bg-flame-orange/10 p-2 rounded-full transition-all duration-300 shadow-lg`}
      title={audioMuted ? "Enable Nexus Ambient Sound" : "Mute Nexus Ambient Sound"}
    >
      {audioMuted ? (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      ) : (
        <div className="relative">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828a1 1 0 010-1.415z" clipRule="evenodd" />
          </svg>
          <span className="absolute -top-1 -right-1 flex h-2 w-2">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-flame-orange opacity-75"></span>
            <span className="relative inline-flex rounded-full h-2 w-2 bg-flame-orange"></span>
          </span>
        </div>
      )}
    </button>
  );
}
