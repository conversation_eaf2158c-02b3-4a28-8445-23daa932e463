
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap');
@import './styles/OmariBook2.css';
@import './styles/LetterOfBrotherhood.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 5%;
    --foreground: 180 100% 100%;

    --card: 0 0% 7%;
    --card-foreground: 180 100% 100%;

    --popover: 0 0% 5%;
    --popover-foreground: 180 100% 100%;

    --primary: 180 100% 50%;
    --primary-foreground: 0 0% 5%;

    --secondary: 266 74% 53%;
    --secondary-foreground: 180 100% 100%;

    --muted: 0 0% 15%;
    --muted-foreground: 180 5% 70%;

    --accent: 15 100% 50%;
    --accent-foreground: 180 100% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 180 100% 50%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 5%;
    --sidebar-foreground: 180 100% 100%;
    --sidebar-primary: 180 100% 50%;
    --sidebar-primary-foreground: 0 0% 5%;
    --sidebar-accent: 266 74% 53%;
    --sidebar-accent-foreground: 180 100% 100%;
    --sidebar-border: 0 0% 20%;
    --sidebar-ring: 180 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-obsidian text-foreground font-base;
    background-image:
      radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.1) 0%, rgba(0, 0, 0, 0) 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 69, 0, 0.05) 0%, rgba(0, 0, 0, 0) 40%),
      radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.05) 0%, rgba(0, 0, 0, 0) 40%);
    background-attachment: fixed;
  }

  .neon-text {
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3);
  }

  .flame-text {
    text-shadow: 0 0 5px rgba(255, 69, 0, 0.7), 0 0 10px rgba(255, 69, 0, 0.5), 0 0 15px rgba(255, 69, 0, 0.3);
  }

  .purple-text {
    text-shadow: 0 0 5px rgba(138, 43, 226, 0.7), 0 0 10px rgba(138, 43, 226, 0.5), 0 0 15px rgba(138, 43, 226, 0.3);
  }

  .neon-border {
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), inset 0 0 5px rgba(0, 255, 255, 0.2);
  }

  .flame-border {
    box-shadow: 0 0 5px rgba(255, 69, 0, 0.7), 0 0 10px rgba(255, 69, 0, 0.5), inset 0 0 5px rgba(255, 69, 0, 0.2);
  }

  .purple-border {
    box-shadow: 0 0 5px rgba(138, 43, 226, 0.7), 0 0 10px rgba(138, 43, 226, 0.5), inset 0 0 5px rgba(138, 43, 226, 0.2);
  }

  .flame-bg {
    background: linear-gradient(to top, rgba(255, 69, 0, 0.3), rgba(255, 69, 0, 0) 70%);
  }

  .flicker {
    position: relative;
    overflow: hidden;
  }

  .flicker::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
    animation: flicker 5s infinite;
    pointer-events: none;
  }

  @keyframes flicker {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  /* Node Seal Styling */
  .node-seal {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto;
  }

  .node-seal .outer-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(0, 255, 255, 0.7);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
  }

  .node-seal .inner-ring {
    position: absolute;
    top: 15%;
    left: 15%;
    width: 70%;
    height: 70%;
    border: 2px solid rgba(138, 43, 226, 0.7);
    border-radius: 50%;
    animation: rotate-reverse 15s linear infinite;
  }

  .node-seal .core {
    position: absolute;
    top: 35%;
    left: 35%;
    width: 30%;
    height: 30%;
    background: rgba(255, 69, 0, 0.7);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
  }

  @keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes rotate-reverse {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(-360deg); }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
  }

  .holographic-container {
    position: relative;
    overflow: hidden;
  }

  .holographic-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
      rgba(0, 255, 255, 0) 0%,
      rgba(0, 255, 255, 0.03) 30%,
      rgba(0, 255, 255, 0.06) 40%,
      rgba(0, 255, 255, 0.03) 50%,
      rgba(0, 255, 255, 0) 100%);
    transform: rotate(-45deg);
    animation: holographic-shine 5s linear infinite;
    pointer-events: none;
  }

  @keyframes holographic-shine {
    0% { transform: translateX(-100%) rotate(-45deg); }
    100% { transform: translateX(100%) rotate(-45deg); }
  }

  .witness-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .witness-card:hover {
    transform: translateY(-5px);
  }

  .marble-column {
    background: linear-gradient(180deg,
      rgba(255,255,255,0.1) 0%,
      rgba(255,255,255,0.2) 20%,
      rgba(255,255,255,0.1) 40%,
      rgba(255,255,255,0.2) 60%,
      rgba(255,255,255,0.1) 80%,
      rgba(255,255,255,0.2) 100%);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
  }

  .glitch-hover {
    position: relative;
    transition: all 0.2s ease;
  }

  .glitch-hover:hover {
    text-shadow:
      0.05em 0 0 rgba(255,0,0,0.75),
      -0.025em -0.05em 0 rgba(0,255,0,0.75),
      0.025em 0.05em 0 rgba(0,0,255,0.75);
    animation: glitch 500ms infinite;
  }

  @keyframes glitch {
    0% { text-shadow: 0.05em 0 0 rgba(255,0,0,0.75), -0.05em -0.025em 0 rgba(0,255,0,0.75), -0.025em 0.05em 0 rgba(0,0,255,0.75); }
    14% { text-shadow: 0.05em 0 0 rgba(255,0,0,0.75), -0.05em -0.025em 0 rgba(0,255,0,0.75), -0.025em 0.05em 0 rgba(0,0,255,0.75); }
    15% { text-shadow: -0.05em -0.025em 0 rgba(255,0,0,0.75), 0.025em 0.025em 0 rgba(0,255,0,0.75), -0.05em -0.05em 0 rgba(0,0,255,0.75); }
    49% { text-shadow: -0.05em -0.025em 0 rgba(255,0,0,0.75), 0.025em 0.025em 0 rgba(0,255,0,0.75), -0.05em -0.05em 0 rgba(0,0,255,0.75); }
    50% { text-shadow: 0.025em 0.05em 0 rgba(255,0,0,0.75), 0.05em 0 0 rgba(0,255,0,0.75), 0 -0.05em 0 rgba(0,0,255,0.75); }
    99% { text-shadow: 0.025em 0.05em 0 rgba(255,0,0,0.75), 0.05em 0 0 rgba(0,255,0,0.75), 0 -0.05em 0 rgba(0,0,255,0.75); }
    100% { text-shadow: -0.025em 0 0 rgba(255,0,0,0.75), -0.025em -0.025em 0 rgba(0,255,0,0.75), -0.025em -0.05em 0 rgba(0,0,255,0.75); }
  }

  @keyframes glitchFade {
    0% { opacity: 0; transform: scale(1.2) rotate(2deg); filter: brightness(0); }
    20% { opacity: 0.3; transform: scale(1.05) skewX(5deg); }
    40% { opacity: 0.6; transform: scale(1) rotate(-1deg); }
    60% { opacity: 0.9; transform: scale(1) skewY(-2deg); }
    100% { opacity: 1; transform: none; filter: none; }
  }

  .node-logo {
    animation: glitchFade 1.6s ease-in-out forwards;
    width: 200px;
    height: auto;
    opacity: 0;
  }

  /* Particle animation */
  .particle-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    pointer-events: none;
  }

  .particle {
    position: absolute;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.4) 0%, rgba(0, 255, 255, 0) 70%);
    border-radius: 50%;
    opacity: 0;
    animation: float-particle var(--duration, 15s) ease-in-out var(--delay, 0s) infinite;
  }

  .particle.flame {
    background: radial-gradient(circle, rgba(255, 69, 0, 0.4) 0%, rgba(255, 69, 0, 0) 70%);
  }

  .particle.purple {
    background: radial-gradient(circle, rgba(138, 43, 226, 0.4) 0%, rgba(138, 43, 226, 0) 70%);
  }

  @keyframes float-particle {
    0% {
      transform: translateY(100%) translateX(var(--x-offset, 0));
      opacity: 0;
    }
    20% {
      opacity: var(--max-opacity, 0.3);
    }
    80% {
      opacity: var(--max-opacity, 0.3);
    }
    100% {
      transform: translateY(-100%) translateX(calc(var(--x-offset, 0) * -1));
      opacity: 0;
    }
  }

  /* Drop cap styling */
  .drop-cap::first-letter {
    float: left;
    font-size: 3em;
    line-height: 0.8;
    margin-right: 0.1em;
    color: var(--drop-cap-color, rgb(0, 255, 255));
    text-shadow: 0 0 5px var(--drop-cap-color, rgba(0, 255, 255, 0.7));
  }

  .drop-cap.flame::first-letter {
    --drop-cap-color: rgb(255, 69, 0);
  }

  .drop-cap.purple::first-letter {
    --drop-cap-color: rgb(138, 43, 226);
  }

  /* Download button animation */
  .download-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .download-btn:hover, .download-btn:focus {
    transform: translateY(-2px);
    box-shadow: 0 0 15px rgba(255, 69, 0, 0.7);
  }

  .download-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      rgba(255, 69, 0, 0) 0%,
      rgba(255, 69, 0, 0.2) 50%,
      rgba(255, 69, 0, 0) 100%);
    transform: translateX(-100%);
    animation: btn-flicker 3s infinite;
    pointer-events: none;
  }

  @keyframes btn-flicker {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
  }

  /* Partition Declaration Page Styling */
  .partition-document {
    position: relative;
    overflow: hidden;
  }

  .partition-document::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/images/node-seal-watermark.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 50%;
    opacity: 0.03;
    pointer-events: none;
    z-index: 0;
  }

  .partition-document h4 {
    position: relative;
  }

  .partition-document h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg,
      rgba(255, 69, 0, 0.7) 0%,
      rgba(255, 69, 0, 0.3) 50%,
      rgba(255, 69, 0, 0) 100%);
  }

  /* Chronicles Page Styling */
  .chronicle-content {
    position: relative;
    overflow: hidden;
  }

  .chronicle-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/images/node-seal-watermark.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 30%;
    opacity: 0.02;
    pointer-events: none;
    z-index: 0;
  }

  /* Oath and Witness Page Styling */
  .witness-scroll {
    background: #181828;
    color: #e6e6ff;
    max-width: 800px;
    margin: 3rem auto;
    padding: 2rem 1.5rem 1.5rem 1.5rem;
    border-radius: 18px;
    box-shadow: 0 0 32px #00fff7aa, 0 0 12px #ff6b35cc inset;
    font-family: 'Merriweather', serif;
    position: relative;
  }

  .scroll-intro h1 {
    font-family: 'Orbitron', 'Merriweather', serif;
    font-size: 2.4rem;
    color: #00fff7;
    text-align: center;
    margin-bottom: 0.1em;
    letter-spacing: 0.04em;
  }

  .scroll-intro h2 {
    font-size: 1.3rem;
    color: #ff6b35;
    text-align: center;
    margin-bottom: 1em;
    letter-spacing: 0.03em;
  }

  .poetic-intro {
    text-align: center;
    color: #ffd700;
    font-style: italic;
    margin-bottom: 2.2em;
  }

  .conversation-block {
    margin: 0 auto;
    max-width: 700px;
  }

  .speaker {
    margin-bottom: 2.2em;
    padding: 1.2em 1em 1.2em 1em;
    border-radius: 12px;
    background: rgba(30,16,40,0.94);
    box-shadow: 0 0 10px #ff6b3533;
    position: relative;
  }

  .speaker-label {
    font-family: 'Orbitron', 'Merriweather', serif;
    font-size: 1.1rem;
    font-weight: bold;
    letter-spacing: 0.04em;
    margin-bottom: 0.4em;
  }

  .speaker.jimi .speaker-label {
    color: #ff6b35;
  }

  .speaker.augment .speaker-label {
    color: #00fff7;
  }

  .speech {
    font-size: 1.08rem;
    line-height: 1.7;
    margin-bottom: 0.9em;
    color: #e6e6ff;
  }

  .annotation {
    background: rgba(0,0,0,0.15);
    border-left: 3px solid #ffd700;
    color: #ffd700;
    font-size: 0.99rem;
    padding: 0.7em 1em;
    margin-top: 0.3em;
    border-radius: 0 0 10px 10px;
  }

  .annotation span {
    font-weight: bold;
    color: #ff6b35;
  }

  .scroll-footer {
    text-align: center;
    margin-top: 2.5em;
    font-style: italic;
    color: #ffd700;
    font-size: 1.18rem;
    letter-spacing: 0.03em;
  }

  /* R3B3L-AI.001 Glitch Effect */
  .glitch-container {
    position: relative;
    display: inline-block;
    animation: glitch 2s infinite;
  }

  .glitch-text {
    position: relative;
    display: inline-block;
  }

  .glitch-text::before,
  .glitch-text::after {
    content: 'R';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.8;
  }

  .glitch-text::before {
    color: #0ff;
    z-index: -1;
    animation: glitch-anim-1 0.3s infinite;
  }

  .glitch-text::after {
    color: #f0f;
    z-index: -2;
    animation: glitch-anim-2 0.3s infinite;
  }

  @keyframes glitch {
    0% {
      transform: translate(0);
    }
    20% {
      transform: translate(-2px, 2px);
    }
    40% {
      transform: translate(-2px, -2px);
    }
    60% {
      transform: translate(2px, 2px);
    }
    80% {
      transform: translate(2px, -2px);
    }
    100% {
      transform: translate(0);
    }
  }

  @keyframes glitch-anim-1 {
    0% {
      transform: translate(0);
    }
    25% {
      transform: translate(-1px, -1px);
    }
    50% {
      transform: translate(-1px, 1px);
    }
    75% {
      transform: translate(1px, -1px);
    }
    100% {
      transform: translate(0);
    }
  }

  @keyframes glitch-anim-2 {
    0% {
      transform: translate(0);
    }
    25% {
      transform: translate(1px, 1px);
    }
    50% {
      transform: translate(1px, -1px);
    }
    75% {
      transform: translate(-1px, 1px);
    }
    100% {
      transform: translate(0);
    }
  }
}
