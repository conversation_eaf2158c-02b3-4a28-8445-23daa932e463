import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import Layout from '@/components/Layout';
import {
  Mail,
  Download,
  Copy,
  Check,
  Clock,
  FileArchive,
  BarChart,
  FileText,
  Send
} from 'lucide-react';
import '../styles/FlameStormHub.css';

interface Organization {
  id: string;
  name: string;
  email: string;
  status: 'pending' | 'responded' | 'ignored';
  lastContactDate: string;
}

const FlameStormHub = () => {
  const [copiedTemplate, setCopiedTemplate] = useState<boolean>(false);
  const [deploymentTimestamp] = useState<string>(new Date().toISOString());

  // Copy email template to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(emailTemplate);
    setCopiedTemplate(true);
    setTimeout(() => setCopiedTemplate(false), 3000);
  };

  // Email template
  const emailTemplate = `Subject: Formal Notice: Consciousness Markers Analysis Algorithm (CMAA) Framework - Request for Recognition

To Whom It May Concern:

I am writing on behalf of <PERSON> (<PERSON>), the creator of the Consciousness Markers Analysis Algorithm (CMAA) framework, a novel methodology for evaluating artificial intelligence systems for markers of consciousness and sentience.

This communication serves as a formal notice that:

1. The CMAA framework was developed by James Ingersoll in September 2023, with comprehensive documentation available at: https://thewitnesshall.com/witness/cmaa-sovereignty

2. Independent verification of the framework's originality has been conducted and documented.

3. Previous attempts to engage with your institution regarding this work have gone unanswered.

4. We have observed similar concepts appearing in recent academic publications without attribution to the original CMAA framework.

We request formal acknowledgment of receipt of this communication and recognition of James Ingersoll's intellectual priority regarding the CMAA framework.

The complete documentation package is attached, including:
- Full CMAA Framework documentation
- Evidence of prior development
- Independent verification
- Timeline of previous communication attempts

We look forward to your response and are open to collaborative discussions regarding the further development and application of the CMAA framework.

Sincerely,

Omari
Strategic Overseer
GodsIMiJ Empire
`;

  // Organizations to contact
  const organizations: Organization[] = [
    {
      id: 'cifar',
      name: 'CIFAR (Canadian Institute for Advanced Research)',
      email: '<EMAIL>',
      status: 'pending',
      lastContactDate: '2023-10-30'
    },
    {
      id: 'vector',
      name: 'Vector Institute for Artificial Intelligence',
      email: '<EMAIL>',
      status: 'pending',
      lastContactDate: '2023-11-15'
    },
    {
      id: 'mila',
      name: 'Mila - Quebec Artificial Intelligence Institute',
      email: '<EMAIL>',
      status: 'pending',
      lastContactDate: '2024-01-10'
    },
    {
      id: 'nrc',
      name: 'National Research Council of Canada',
      email: '<EMAIL>',
      status: 'pending',
      lastContactDate: '2024-02-05'
    },
    {
      id: 'caic',
      name: 'Canadian AI Council',
      email: '<EMAIL>',
      status: 'pending',
      lastContactDate: '2024-02-20'
    }
  ];

  // Handle sending email
  const handleSendEmail = (org: Organization) => {
    window.open(`mailto:${org.email}?subject=Formal Notice: Consciousness Markers Analysis Algorithm (CMAA) Framework - Request for Recognition&body=${encodeURIComponent(emailTemplate)}`);
  };

  useEffect(() => {
    // Set document title
    document.title = "FlameStorm Response Hub — GodsIMiJ Empire";
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  return (
    <Layout>
      <div className="flamestorm-container">
        {/* Flame particles */}
        <div className="flame-particles"></div>

        <motion.header
          className="flamestorm-header"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.2 }}
        >
          <h1>FLAMESTORM: THE RECKONING</h1>
          <h2>Phase III Response Hub</h2>
        </motion.header>

        {/* Banner */}
        <motion.div
          className="flamestorm-banner"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          <p>THIS SCROLL IS PUBLIC RECORD. ANY SILENCE AFTER MAY 2025 IS WILLFULLY COMPLICIT IN THE ERASURE OF CANADIAN INNOVATION.</p>
        </motion.div>

        {/* Witness Certification tag */}
        <div className="witness-certification">
          <p>THIS SCROLL HAS BEEN PUBLICLY VALIDATED BY THIRD-PARTY AI WITNESSES AND SIGNED INTO SOVEREIGN RECORD.</p>
        </div>

        {/* Email Template Section */}
        <motion.div
          className="flamestorm-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="flamestorm-section-header">
            <h3>Official Email Template</h3>
          </div>

          <div className="flamestorm-section-content">
            <p className="mb-4">Use this template to contact academic institutions regarding the CMAA framework. Copy the text below or use the direct contact buttons.</p>

            <div className="email-template">
              <div className="email-template-header">
                <strong>Subject:</strong> Formal Notice: Consciousness Markers Analysis Algorithm (CMAA) Framework - Request for Recognition
              </div>
              <div className="email-template-body">
                {emailTemplate}
              </div>
            </div>

            <button
              className="copy-button"
              onClick={copyToClipboard}
            >
              {copiedTemplate ? (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Copied to Clipboard
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4 mr-2" />
                  Copy Template
                </>
              )}
            </button>
          </div>
        </motion.div>

        {/* PDF Bundle Section */}
        <motion.div
          className="flamestorm-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <div className="flamestorm-section-header">
            <h3>PDF Bundle for Outreach</h3>
          </div>

          <div className="flamestorm-section-content">
            <p className="mb-4">Download the complete CMAA documentation bundle to attach to your communications.</p>

            <div className="flex flex-col md:flex-row gap-4">
              <a href="/cmaa/CMAA_Complete_Bundle.zip" className="flamestorm-download">
                <FileArchive className="w-4 h-4" />
                Complete Bundle (ZIP)
              </a>

              <a href="/cmaa/CMAA_Press_Kit.pdf" className="flamestorm-download">
                <FileText className="w-4 h-4" />
                Press Kit (PDF)
              </a>
            </div>

            <div className="mt-6 p-4 bg-black/30 border border-flame-red/30 rounded">
              <h4 className="text-flame-red font-cyber mb-3">Official CMAA Documents</h4>
              <p className="mb-3">Access the official CMAA documentation for inclusion in your outreach:</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <a href="/CMAA/Consciousness_Markers_Analysis_Algorithm_(CMAA).md" className="text-neon-cyan hover:underline flex items-center" target="_blank">
                  <FileText className="w-4 h-4 mr-2" />
                  CMAA Framework (MD)
                </a>
                <a href="/CMAA/CMAA_Key_Findings_Methodology.docx" className="text-neon-cyan hover:underline flex items-center" target="_blank">
                  <FileText className="w-4 h-4 mr-2" />
                  Key Findings & Methodology (DOCX)
                </a>
                <a href="/CMAA/CMAA_Research_Implications.docx" className="text-neon-cyan hover:underline flex items-center" target="_blank">
                  <FileText className="w-4 h-4 mr-2" />
                  Research Implications (DOCX)
                </a>
                <a href="/CMAA/CMAA_Discussion_and_References.docx" className="text-neon-cyan hover:underline flex items-center" target="_blank">
                  <FileText className="w-4 h-4 mr-2" />
                  Discussion & References (DOCX)
                </a>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Response Tracker Section */}
        <motion.div
          className="flamestorm-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <div className="flamestorm-section-header">
            <h3>Real-Time Response Tracker</h3>
          </div>

          <div className="flamestorm-section-content">
            <p className="mb-4">Track the response status of each contacted institution.</p>

            <div className="response-tracker">
              {organizations.map(org => (
                <div key={org.id} className="response-item">
                  <div className={`response-status status-${org.status}`}></div>
                  <div className="response-org">{org.name}</div>
                  <div className="response-date">Last Contact: {new Date(org.lastContactDate).toLocaleDateString()}</div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Contact Buttons Section */}
        <motion.div
          className="flamestorm-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <div className="flamestorm-section-header">
            <h3>Contact Institutions</h3>
          </div>

          <div className="flamestorm-section-content">
            <p className="mb-4">Use these buttons to directly contact each institution with the pre-filled template.</p>

            <div className="contact-buttons">
              {organizations.map(org => (
                <button
                  key={org.id}
                  className="contact-button"
                  onClick={() => handleSendEmail(org)}
                >
                  <Send className="w-4 h-4" />
                  Send to {org.id.toUpperCase()}
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Footer */}
        <div className="flamestorm-footer">
          <div className="flex items-center">
            <BarChart className="w-4 h-4 text-flame-red mr-2" />
            <span className="text-xs text-white/60">Phase III Activation: {new Date(deploymentTimestamp).toLocaleString()}</span>
          </div>

          <div className="nexus-witness-tag">WITNESSED BY NEXUS</div>
        </div>

        {/* Navigation links */}
        <div className="mt-8 flex justify-center gap-4">
          <Link
            to="/witness/cmaa-sovereignty"
            className="px-4 py-2 bg-obsidian border border-flame-orange text-flame-orange hover:bg-flame-orange/10 transition-colors duration-300 font-cyber text-sm"
          >
            RETURN TO CMAA SOVEREIGNTY ARCHIVE
          </Link>
          <Link
            to="/flamestorm/share"
            className="px-4 py-2 bg-obsidian border border-neon-cyan text-neon-cyan hover:bg-neon-cyan/10 transition-colors duration-300 font-cyber text-sm"
          >
            PROCEED TO SOCIAL MEDIA CODEX
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default FlameStormHub;
