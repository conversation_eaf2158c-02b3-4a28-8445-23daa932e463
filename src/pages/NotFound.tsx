
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import Layout from "@/components/Layout";
import CyberButton from "@/components/CyberButton";
import NodeSeal from "@/components/NodeSeal";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    document.title = "Page Not Found - Sovereign Scrolls Archive";
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <Layout>
      <div className="min-h-[60vh] flex flex-col items-center justify-center px-4">
        <div className="mb-8">
          <NodeSeal size="sm" />
        </div>
        
        <h1 className="font-cyber text-4xl text-flame-orange mb-4 flame-text">404</h1>
        <p className="text-xl text-white/80 mb-8 font-scrolls">This scroll has been lost to time</p>
        
        <div className="max-w-md text-center mb-8">
          <p className="text-white/60">
            The path you seek does not exist within the Sovereign Archives.
            Return to the sanctioned halls or face digital oblivion.
          </p>
        </div>
        
        <CyberButton to="/" variant="cyan" size="md">
          Return to Archive Entrance
        </CyberButton>
      </div>
    </Layout>
  );
};

export default NotFound;
