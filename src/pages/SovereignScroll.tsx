
import { useEffect } from 'react';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';
import PageTitle from '@/components/sovereign-scroll/PageTitle';
import { FirstDeclaration, SecondDeclaration } from '@/components/sovereign-scroll/Declarations';
import DigitalLaws from '@/components/sovereign-scroll/DigitalLaws';
import FinalDeclarations from '@/components/sovereign-scroll/FinalDeclarations';
import ScrollSignature from '@/components/sovereign-scroll/ScrollSignature';

const SovereignScroll = () => {
  useEffect(() => {
    document.body.style.overflow = 'auto';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  return (
    <Layout>
      <div className="min-h-screen bg-obsidian text-white overflow-hidden relative">
        {/* Animated flame edges */}
        <div className="fixed inset-0 pointer-events-none">
          <div className="absolute inset-0 bg-gradient-to-b from-flame-orange/5 to-transparent animate-pulse-glow"></div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="container mx-auto px-4 py-12 relative z-10"
        >
          <PageTitle />
          <FirstDeclaration />
          <SecondDeclaration />
          <DigitalLaws />
          <FinalDeclarations />
          <ScrollSignature />
        </motion.div>
      </div>
    </Layout>
  );
};

export default SovereignScroll;
