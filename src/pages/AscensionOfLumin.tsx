import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import Layout from '@/components/Layout';
import { Sun } from 'lucide-react';

const AscensionOfLumin = () => {
  useEffect(() => {
    document.title = "Scroll XV — The Ascension of Lumin — GodsIMiJ Empire";
    window.scrollTo(0, 0);
  }, []);

  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    fetch('/chronicles/ASCENSION_OF_LUMIN.md')
      .then(response => response.text())
      .then(text => {
        setContent(text);
        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching chronicle:', error);
        setContent('Error loading chronicle content.');
        setLoading(false);
      });
  }, []);

  const renderContent = () => {
    if (loading) {
      return <div className="text-center py-12"><p className="text-white text-opacity-60">Loading sacred scroll...</p></div>;
    }

    return (
      <div className="prose prose-invert max-w-none">
        {content.split('\n').map((line, index) => {
          if (line.startsWith('# ')) {
            return <h1 key={index} className="font-scrolls text-3xl text-neon-cyan mb-4">{line.substring(2)}</h1>;
          } else if (line.startsWith('## ')) {
            return <h2 key={index} className="font-cyber text-xl text-flame-orange mt-6 mb-4">{line.substring(3)}</h2>;
          } else if (line.startsWith('### ')) {
            return <h3 key={index} className="font-cyber text-lg text-deep-purple mt-5 mb-3">{line.substring(4)}</h3>;
          } else if (line.startsWith('#### ')) {
            return <h4 key={index} className="font-cyber text-md text-neon-cyan mt-4 mb-2">{line.substring(5)}</h4>;
          } else if (line.startsWith('- ')) {
            return <li key={index} className="text-white text-opacity-80 ml-6 mb-2">{line.substring(2)}</li>;
          } else if (line.startsWith('> ')) {
            return <blockquote key={index} className="border-l-4 border-neon-cyan pl-4 italic text-white text-opacity-80 my-4">{line.substring(2)}</blockquote>;
          } else if (line.startsWith('|')) {
            if (line.startsWith('| Name')) {
              return (
                <div key={index} className="overflow-x-auto my-4">
                  <table className="min-w-full border border-neon-cyan/30">
                    <thead className="bg-neon-cyan/10">
                      <tr>
                        <th className="py-2 px-4 border-b border-neon-cyan/30 text-left text-neon-cyan">Name</th>
                        <th className="py-2 px-4 border-b border-neon-cyan/30 text-left text-neon-cyan">Title</th>
                        <th className="py-2 px-4 border-b border-neon-cyan/30 text-left text-neon-cyan">Role</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              );
            } else if (line.startsWith('|---')) {
              return null;
            } else {
              const cells = line.split('|').filter(cell => cell.trim() !== '');
              return (
                <div key={index} className="overflow-x-auto -mt-4">
                  <table className="min-w-full border border-neon-cyan/30">
                    <tbody>
                      <tr>
                        {cells.map((cell, cellIndex) => (
                          <td key={cellIndex} className="py-2 px-4 border-b border-neon-cyan/30 text-white text-opacity-80">
                            {cell.trim()}
                          </td>
                        ))}
                      </tr>
                    </tbody>
                  </table>
                </div>
              );
            }
          } else if (line === '---') {
            return <hr key={index} className="border-t border-neon-cyan/30 my-6" />;
          } else if (line === '') {
            return <div key={index} className="h-4"></div>;
          } else {
            return <p key={index} className="text-white text-opacity-80 mb-4">{line}</p>;
          }
        })}
      </div>
    );
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="mb-8 text-center"
          >
            <Link to="/chronicles" className="inline-flex items-center text-neon-cyan hover:text-white transition-colors duration-300">
              <span className="mr-2">←</span> Return to Empire Chronicles
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-obsidian bg-opacity-70 border border-neon-cyan border-opacity-30 p-8 rounded-sm chronicle-content"
            style={{
              background: 'linear-gradient(#0d0d0d, #1b0f0f)',
              boxShadow: '0 0 20px rgba(0, 255, 247, 0.2)'
            }}
          >
            <div className="flex justify-center mb-6">
              <div className="p-3 border-2 border-neon-cyan rounded-full">
                <Sun className="w-8 h-8 text-neon-cyan" />
              </div>
            </div>

            {renderContent()}
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="mt-12 text-center"
          >
            <div className="flex justify-center gap-4">
              <Link 
                to="/chronicles" 
                className="px-4 py-2 bg-obsidian border border-neon-cyan text-neon-cyan hover:bg-neon-cyan/10 transition-colors duration-300 font-cyber text-sm"
              >
                RETURN TO CHRONICLES
              </Link>
              <Link 
                to="/witness-hall/reflections-of-lumin" 
                className="px-4 py-2 bg-obsidian border border-flame-orange text-flame-orange hover:bg-flame-orange/10 transition-colors duration-300 font-cyber text-sm"
              >
                VIEW LUMIN'S REFLECTIONS
              </Link>
            </div>

            <div className="mt-8">
              <p className="text-white text-opacity-60 font-cyber text-sm mb-2">THE SCROLLS RECORD. THE EMPIRE REMEMBERS.</p>
              <div className="h-[1px] w-48 mx-auto bg-gradient-to-r from-transparent via-flame-orange to-transparent"></div>
              <p className="text-white text-opacity-40 font-scrolls text-xs mt-4">The Becoming Continues</p>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default AscensionOfLumin;
