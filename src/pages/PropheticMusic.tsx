
import Layout from "@/components/Layout";
import CyberButton from "@/components/CyberButton";

const PropheticMusic = () => {
  const videos = [
    {
      id: "uQVkwQJBCUI",
      title: "Prophetic Music 1"
    },
    {
      id: "Xg8cNUy6WD4",
      title: "Prophetic Music 2"
    },
    {
      id: "VOckzTMsSSw",
      title: "Prophetic Music 3"
    },
    {
      id: "tFLdH9_p53c",
      title: "Prophetic Music 4"
    }
  ];

  return (
    <Layout className="relative">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Title */}
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-cyber text-neon-cyan text-center mb-8 neon-text">
          Prophetic Music
        </h1>

        {/* Videos Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
          {videos.map((video) => (
            <div key={video.id} className="relative aspect-video bg-black/30 rounded-lg overflow-hidden hover:transform hover:scale-[1.02] transition-transform border border-neon-cyan/30 neon-border">
              <iframe
                className="absolute inset-0 w-full h-full"
                src={`https://www.youtube.com/embed/${video.id}`}
                title={video.title}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                referrerPolicy="strict-origin-when-cross-origin"
              />
            </div>
          ))}
        </div>

        {/* Return Button */}
        <div className="mt-16 flex justify-center">
          <CyberButton 
            to="/"
            variant="cyan"
            size="lg"
            className="animate-pulse-slow"
          >
            Return to Archives
          </CyberButton>
        </div>
      </div>

      {/* Background Node Watermark */}
      <div className="absolute inset-0 pointer-events-none opacity-10 flex items-center justify-center">
        <div className="w-[600px] h-[600px] node-seal" />
      </div>
    </Layout>
  );
};

export default PropheticMusic;
