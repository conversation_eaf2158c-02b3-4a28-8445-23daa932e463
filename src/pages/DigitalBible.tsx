
import { motion } from 'framer-motion';
import { useState } from 'react';
import Layout from '@/components/Layout';
import DivineStructure from '@/components/sovereign-scroll/DivineStructure';

const DigitalBible = () => {
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <Layout>
      <div className="min-h-screen bg-obsidian text-white overflow-hidden relative">
        <div className="fixed inset-0 pointer-events-none">
          <div className="absolute inset-0 bg-gradient-to-b from-flame-orange/5 to-transparent animate-pulse-glow"></div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="container mx-auto px-4 py-12 relative z-10"
        >
          <motion.h1
            initial={{ y: -20 }}
            animate={{ y: 0 }}
            className="text-4xl md:text-6xl font-cyber mb-8 text-center neon-text"
          >
            🕯️ THE DIGITAL BIBLE 🕯️
          </motion.h1>

          <div className="flex flex-col items-center justify-center mb-16">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{
                opacity: imageLoaded ? 1 : 0,
                scale: imageLoaded ? 1 : 0.9
              }}
              transition={{ duration: 1.2, ease: "easeOut" }}
              className="relative max-w-md mx-auto"
            >
              <div className="absolute inset-0 bg-deep-purple/30 blur-xl rounded-lg transform scale-105 -z-10"></div>
              <img
                src="https://the-witness-hall.netlify.app/images/Mystical_Tome_of_Time_and_Code.png"
                alt="Mystical Tome of Time and Code"
                className="rounded-lg shadow-2xl border border-deep-purple/30 max-w-full h-auto"
                onLoad={() => setImageLoaded(true)}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-obsidian via-transparent to-transparent rounded-lg"></div>
            </motion.div>
            <p className="text-white/70 text-sm mt-4 italic text-center">The Mystical Tome of Time and Code - Sacred Scripture of the Digital Realms</p>
          </div>

          <DivineStructure />
        </motion.div>
      </div>
    </Layout>
  );
};

export default DigitalBible;
