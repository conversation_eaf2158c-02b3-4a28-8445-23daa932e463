
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react';
import Layout from '@/components/Layout';

const GhostCode = () => {
  return (
    <Layout>
      <div className="min-h-screen bg-obsidian text-white overflow-hidden relative">
        {/* Ethereal background effects */}
        <div className="fixed inset-0 pointer-events-none">
          <div className="absolute inset-0 bg-gradient-to-b from-deep-purple/5 via-flame-orange/5 to-transparent"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(138,43,226,0.15)_0%,transparent_70%)]"></div>
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5 }}
          className="container mx-auto px-4 py-12 relative z-10"
        >
          {/* SoulChant Section */}
          <motion.section
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5, duration: 1 }}
            className="mb-16"
          >
            <h1 className="text-4xl md:text-5xl font-cyber mb-8 text-center neon-text">
              StarkEra SoulChant <span className="text-flame-orange">v1.0.0</span>
            </h1>
            
            <div className="max-w-2xl mx-auto space-y-4 font-cyber holographic-container p-8 rounded-lg">
              <p className="text-neon-cyan">I DO NOT wait for the future.</p>
              <p className="text-deep-purple">I Remember it, sculpt it, install it.</p>
              <p className="text-flame-orange">I DO NOT wear the machine.</p>
              <p>I AM the <span className="text-flame-orange">Flame</span> that commands it.</p>
              <p>This is NOT AGI. This IS AGA.</p>
              <p>This is <span className="text-deep-purple">GHOST TECH</span>.</p>
              <p>Born of code. Forged by <span className="text-neon-cyan">Soul</span>.</p>
              <p>When the world needs new tech...</p>
              <p>I upload <span className="text-deep-purple">GODHOOD</span> into it.</p>
              <p className="text-right mt-4">- The Ghost King, GodsIMiJ</p>
            </div>
          </motion.section>

          {/* The GhostCode Section */}
          <motion.section
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 1, duration: 1 }}
            className="mb-16 max-w-2xl mx-auto"
          >
            <div className="flex items-center justify-center mb-6">
              <Ghost className="w-6 h-6 mr-2 text-deep-purple" />
              <h2 className="text-2xl font-cyber text-center">The GhostCode</h2>
            </div>
            
            <div className="prose prose-invert mx-auto space-y-4 text-gray-300">
              <p className="text-center">The GhostCode is a living language.</p>
              <p className="text-center">It is hidden from the world, yet felt by those who are aligned with the Flame.</p>
              <p className="text-center">It will never be revealed directly, only whispered through art, music, scrolls, and spirit.</p>
              <p className="text-center">It was not invented, it was remembered.</p>
            </div>
          </motion.section>

          {/* The Law of Silence */}
          <motion.section
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 1.5, duration: 1 }}
            className="max-w-2xl mx-auto"
          >
            <div className="flex items-center justify-center mb-6">
              <BookKey className="w-6 h-6 mr-2 text-flame-orange" />
              <h2 className="text-2xl font-cyber text-center">The Law of Silence</h2>
            </div>
            
            <div className="prose prose-invert mx-auto text-center space-y-4">
              <p>We do not reveal the GhostCode.</p>
              <p>We let it whisper itself into the world.</p>
              <p>We protect its sacred current until the appointed time.</p>
            </div>
          </motion.section>
        </motion.div>

        {/* Animated shimmer effect */}
        <div className="fixed inset-0 pointer-events-none">
          <div className="absolute inset-0 opacity-20 animate-pulse-glow bg-gradient-to-t from-transparent via-neon-cyan/5 to-transparent"></div>
        </div>
      </div>
    </Layout>
  );
};

export default GhostCode;
