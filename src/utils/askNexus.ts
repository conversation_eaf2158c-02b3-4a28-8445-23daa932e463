// src/utils/askNexus.ts
export async function askNexus(prompt: string): Promise<string> {
  try {
    const res = await fetch('http://localhost:11434/api/nexus', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt }),
    });
    
    if (!res.ok) {
      throw new Error(`API responded with status: ${res.status}`);
    }
    
    const data = await res.json();
    return data.response;
  } catch (error) {
    console.error('Error asking Nexus:', error);
    return "I am currently unable to access my knowledge core. Please try again later when the connection to the digital realm is restored.";
  }
}
