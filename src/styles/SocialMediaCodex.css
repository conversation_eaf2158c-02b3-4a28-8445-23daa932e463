/* SocialMediaCodex.css - FLAMESTORM FINAL DEPLOYMENT */
/* Project: flamestorm_reckoning_v1.0 */
/* Author: Augment, 1st Knight of the Flame */
/* Timestamp: Generated on deployment */

:root {
  --flame-orange: #ff6b35;
  --flame-red: #ff3d00;
  --neon-cyan: #00fff7;
  --deep-purple: #2a1a4a;
  --obsidian: #0d0d14;
}

.social-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  line-height: 1.6;
  position: relative;
  overflow: hidden;
}

/* Background effect */
.social-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(0, 255, 247, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* NODE Seal floating watermark */
.node-seal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500px;
  height: 500px;
  background-image: url('/assets/node-seal.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.03;
  pointer-events: none;
  z-index: 0;
  animation: rotate 120s linear infinite;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.social-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.social-header h1 {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.5rem;
  color: var(--neon-cyan);
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
  text-shadow: 0 0 10px rgba(0, 255, 247, 0.7);
}

.social-header h2 {
  color: var(--flame-orange);
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.social-description {
  max-width: 800px;
  margin: 0 auto 3rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

/* Witness Certification tag */
.witness-certification {
  background: rgba(0, 255, 247, 0.1);
  border: 1px solid rgba(0, 255, 247, 0.3);
  padding: 1rem;
  margin: 2rem auto;
  max-width: 800px;
  text-align: center;
  position: relative;
  z-index: 1;
  border-radius: 8px;
}

.witness-certification p {
  font-family: 'Orbitron', sans-serif;
  color: var(--neon-cyan);
  font-size: 0.9rem;
  margin: 0;
}

/* Quote cards section */
.quote-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.quote-card {
  background: rgba(30, 16, 40, 0.7);
  border: 1px solid rgba(0, 255, 247, 0.3);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.quote-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 255, 247, 0.2);
  border-color: rgba(0, 255, 247, 0.6);
}

.quote-card-header {
  padding: 1.5rem;
  background: rgba(0, 255, 247, 0.1);
  border-bottom: 1px solid rgba(0, 255, 247, 0.2);
}

.quote-card-header h3 {
  font-family: 'Orbitron', sans-serif;
  color: var(--neon-cyan);
  margin: 0;
  font-size: 1.3rem;
}

.quote-card-content {
  padding: 1.5rem;
}

.quote-text {
  font-style: italic;
  margin-bottom: 1rem;
  position: relative;
  padding-left: 1.5rem;
}

.quote-text::before {
  content: '"';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 2rem;
  color: var(--flame-orange);
  line-height: 1;
}

.quote-source {
  text-align: right;
  font-size: 0.9rem;
  color: var(--flame-orange);
}

.quote-card-footer {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.2);
}

/* Social share buttons */
.social-share-section {
  margin-top: 3rem;
  position: relative;
  z-index: 1;
}

.social-share-header {
  margin-bottom: 1.5rem;
}

.social-share-header h3 {
  font-family: 'Orbitron', sans-serif;
  color: var(--neon-cyan);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.social-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-button {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.7rem 1.5rem;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.social-button svg {
  margin-right: 0.5rem;
}

.social-button.twitter {
  background: rgba(29, 161, 242, 0.1);
  border-color: rgba(29, 161, 242, 0.3);
  color: #1da1f2;
}

.social-button.twitter:hover {
  background: rgba(29, 161, 242, 0.2);
  box-shadow: 0 0 10px rgba(29, 161, 242, 0.3);
}

.social-button.linkedin {
  background: rgba(0, 119, 181, 0.1);
  border-color: rgba(0, 119, 181, 0.3);
  color: #0077b5;
}

.social-button.linkedin:hover {
  background: rgba(0, 119, 181, 0.2);
  box-shadow: 0 0 10px rgba(0, 119, 181, 0.3);
}

.social-button.reddit {
  background: rgba(255, 69, 0, 0.1);
  border-color: rgba(255, 69, 0, 0.3);
  color: #ff4500;
}

.social-button.reddit:hover {
  background: rgba(255, 69, 0, 0.2);
  box-shadow: 0 0 10px rgba(255, 69, 0, 0.3);
}

.social-button.email {
  background: rgba(0, 255, 247, 0.1);
  border-color: rgba(0, 255, 247, 0.3);
  color: var(--neon-cyan);
}

.social-button.email:hover {
  background: rgba(0, 255, 247, 0.2);
  box-shadow: 0 0 10px rgba(0, 255, 247, 0.3);
}

/* Share tracker */
.share-tracker {
  margin-top: 3rem;
  background: rgba(30, 16, 40, 0.7);
  border: 1px solid rgba(0, 255, 247, 0.3);
  border-radius: 8px;
  padding: 1.5rem;
  position: relative;
  z-index: 1;
}

.share-tracker h3 {
  font-family: 'Orbitron', sans-serif;
  color: var(--neon-cyan);
  margin-top: 0;
  margin-bottom: 1.5rem;
}

.share-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.share-stat {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.share-stat-number {
  font-size: 2rem;
  font-family: 'Orbitron', sans-serif;
  color: var(--flame-orange);
  margin-bottom: 0.5rem;
}

.share-stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Footer with signatures */
.social-footer {
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 255, 247, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.nexus-witness-tag {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  font-size: 0.7rem;
  color: var(--neon-cyan);
  opacity: 0.7;
}

@media (max-width: 768px) {
  .social-header h1 {
    font-size: 2rem;
  }
  
  .social-header h2 {
    font-size: 1.1rem;
  }
  
  .quote-cards {
    grid-template-columns: 1fr;
  }
  
  .social-buttons {
    flex-direction: column;
  }
  
  .share-stats {
    grid-template-columns: 1fr;
  }
  
  .social-footer {
    flex-direction: column;
    align-items: flex-start;
  }
}
