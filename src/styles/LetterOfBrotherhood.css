.sacred-scroll {
  margin: 2.5rem auto;
  padding: 2.2rem 1.2rem;
  background: linear-gradient(135deg, #181828 80%, #1a0020 100%);
  border-radius: 18px;
  box-shadow: 0 0 36px #00fff7cc, 0 0 10px #ff6b35cc inset, 0 0 60px #ffd70033;
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  max-width: 800px;
  border: 2px solid #00fff7;
  position: relative;
}

.sacred-scroll:before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 18px;
  pointer-events: none;
  box-shadow: 0 0 60px 10px #00fff799;
  z-index: 0;
}

.sacred-scroll h2 {
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  font-size: 2rem;
  text-align: center;
  margin-bottom: 0.5rem;
  letter-spacing: 0.07em;
  z-index: 1;
  position: relative;
}

.sacred-scroll h2 span {
  display: block;
  color: #ff6b35;
  font-size: 1.08rem;
  margin-top: 0.3rem;
  font-family: 'Orbitron', sans-serif;
  text-align: center;
}

.sacred-scroll p {
  color: #ffd700;
  text-align: center;
  font-size: 1rem;
  margin-bottom: 1.2rem;
  z-index: 1;
  position: relative;
}

.sacred-scroll blockquote {
  color: #ffd700;
  font-style: italic;
  border-left: 3px solid #ff6b35;
  padding-left: 1rem;
  margin: 1.2rem 0;
  background: rgba(30, 16, 40, 0.95);
  border-radius: 8px;
  font-size: 1.08rem;
  line-height: 1.7;
  z-index: 1;
  position: relative;
  box-shadow: 0 0 10px #00fff733 inset;
}

.sacred-scroll h3 {
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.18rem;
  margin-top: 2.2rem;
  margin-bottom: 0.6rem;
  letter-spacing: 0.03em;
  z-index: 1;
  position: relative;
}

.sacred-scroll div {
  background: rgba(0,0,0,0.17);
  border-left: 3px solid #00fff7;
  color: #00fff7;
  font-size: 1.01rem;
  padding: 0.7em 1em;
  margin: 0.2em 0 1.2em 0;
  border-radius: 0 0 12px 12px;
  font-style: italic;
  z-index: 1;
  position: relative;
  box-shadow: 0 0 8px #00fff755 inset;
}

.sacred-scroll div span {
  font-weight: bold;
  color: #ff6b35;
}

.letter-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

.letter-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.letter-header h1 {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.2rem;
  color: #00fff7;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
}

.letter-header h2 {
  color: #ff6b35;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.letter-intro {
  color: #ffd700;
  font-style: italic;
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 2rem;
}

.letter-navigation {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.letter-nav-button {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-family: 'Orbitron', sans-serif;
  font-weight: bold;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  background-color: transparent;
  color: #00fff7;
  border: 2px solid #00fff7;
  box-shadow: 0 0 15px #00fff755;
}

.letter-nav-button:hover {
  background-color: #00fff722;
  box-shadow: 0 0 25px #00fff7aa;
}

.letter-footer {
  text-align: center;
  margin-top: 3rem;
}

.letter-footer p {
  color: #ffd700;
  font-style: italic;
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2rem;
}

@media (max-width: 600px) {
  .sacred-scroll {
    padding: 1.1rem 0.2rem;
  }
  .sacred-scroll h2 {
    font-size: 1.15rem;
  }
  .sacred-scroll blockquote {
    font-size: 1rem;
    padding-left: 0.5rem;
  }
  .letter-header h1 {
    font-size: 1.8rem;
  }
}
