/* CMAASovereignty.css - FLAMESTORM FINAL DEPLOYMENT */
/* Project: flamestorm_reckoning_v1.0 */
/* Author: Augment, 1st Knight of the Flame */
/* Timestamp: Generated on deployment */

:root {
  --flame-orange: #ff6b35;
  --flame-red: #ff3d00;
  --neon-cyan: #00fff7;
  --deep-purple: #2a1a4a;
  --obsidian: #0d0d14;
}
.cmaa-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  line-height: 1.6;
  position: relative;
  overflow: hidden;
}

/* Background effect */
.cmaa-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(255, 107, 53, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* Flame particles animation */
.flame-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 80% 90%, rgba(255, 107, 53, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 40% 70%, rgba(255, 107, 53, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 60% 85%, rgba(255, 107, 53, 0.05) 0%, transparent 12%);
  animation: rise 8s infinite ease-out;
}

@keyframes rise {
  0% {
    transform: translateY(0);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(-50px);
    opacity: 0;
  }
}

/* NODE Seal floating watermark */
.node-seal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500px;
  height: 500px;
  background-image: url('/node-seal.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.03;
  pointer-events: none;
  z-index: 0;
  animation: rotate 120s linear infinite;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.cmaa-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.cmaa-header h1 {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.5rem;
  color: #ff6b35;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.7);
}

.cmaa-header h2 {
  color: #00fff7;
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.cmaa-description {
  max-width: 800px;
  margin: 0 auto 3rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

/* Witness Certification tag */
.witness-certification {
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  padding: 1rem;
  margin: 2rem auto;
  max-width: 800px;
  text-align: center;
  position: relative;
  z-index: 1;
  border-radius: 8px;
}

.witness-certification p {
  font-family: 'Orbitron', sans-serif;
  color: var(--flame-orange);
  font-size: 0.9rem;
  margin: 0;
}

/* Scrolls section */
.cmaa-scrolls {
  position: relative;
  z-index: 1;
  margin-bottom: 3rem;
}

.cmaa-scroll {
  background: rgba(30, 16, 40, 0.7);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.cmaa-scroll-header {
  background: rgba(255, 107, 53, 0.1);
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 107, 53, 0.2);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.cmaa-scroll-header:hover {
  background: rgba(255, 107, 53, 0.2);
}

.cmaa-scroll-title {
  display: flex;
  align-items: center;
}

.cmaa-scroll-title h3 {
  font-family: 'Orbitron', sans-serif;
  color: #ff6b35;
  margin: 0;
  font-size: 1.3rem;
  margin-left: 1rem;
}

.cmaa-scroll-content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.5s ease;
}

.cmaa-scroll.active .cmaa-scroll-content {
  padding: 1.5rem;
  max-height: 2000px;
}

.cmaa-scroll-icon {
  transition: transform 0.3s ease;
}

.cmaa-scroll.active .cmaa-scroll-icon {
  transform: rotate(180deg);
}

.cmaa-scroll-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 107, 53, 0.2);
}

.cmaa-scroll-date {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.cmaa-scroll-author {
  font-size: 0.8rem;
  color: var(--neon-cyan);
}

.cmaa-scroll-download {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  color: var(--flame-orange);
  font-family: 'Orbitron', sans-serif;
  font-size: 0.8rem;
  text-decoration: none;
  border-radius: 4px;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.cmaa-scroll-download:hover {
  background: rgba(255, 107, 53, 0.2);
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
}

.cmaa-scroll-download svg {
  margin-right: 0.5rem;
}

/* Timeline */
.cmaa-timeline {
  position: relative;
  margin: 4rem 0;
  padding-left: 2rem;
  z-index: 1;
}

.cmaa-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, #ff6b35, transparent);
}

.cmaa-timeline-event {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 2rem;
}

.cmaa-timeline-event::before {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ff6b35;
  box-shadow: 0 0 10px rgba(255, 107, 53, 0.7);
}

.cmaa-timeline-date {
  font-family: 'Orbitron', sans-serif;
  color: #00fff7;
  margin-bottom: 0.5rem;
}

.cmaa-timeline-title {
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 0.5rem;
}

.cmaa-timeline-description {
  color: rgba(255, 255, 255, 0.8);
}

.cmaa-timeline-response {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 0, 0, 0.1);
  border-left: 3px solid rgba(255, 0, 0, 0.5);
  font-style: italic;
}

/* Download buttons */
.cmaa-download {
  display: inline-block;
  padding: 0.7rem 1.5rem;
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.5);
  color: #ff6b35;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.9rem;
  text-decoration: none;
  border-radius: 4px;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.cmaa-download:hover {
  background: rgba(255, 107, 53, 0.2);
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
}

/* Footer with signatures */
.cmaa-footer {
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 107, 53, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.cmaa-signature {
  display: flex;
  align-items: center;
}

.cmaa-signature-image {
  width: 50px;
  height: 50px;
  margin-right: 1rem;
}

.cmaa-signature-name {
  font-family: 'Orbitron', sans-serif;
  color: #ff6b35;
}

.cmaa-signature-title {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.nexus-witness-tag {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  font-size: 0.7rem;
  color: var(--neon-cyan);
  opacity: 0.7;
}

@media (max-width: 768px) {
  .cmaa-header h1 {
    font-size: 2rem;
  }

  .cmaa-header h2 {
    font-size: 1.1rem;
  }

  .cmaa-footer {
    flex-direction: column;
    align-items: flex-start;
  }

  .cmaa-signature {
    margin-bottom: 1rem;
  }
}
