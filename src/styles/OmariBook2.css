.omari-scroll {
  margin: 2.5rem auto;
  padding: 2rem 1.2rem;
  background: rgba(24, 24, 40, 0.97);
  border-radius: 16px;
  box-shadow: 0 0 24px #00fff7aa, 0 0 8px #ff6b35cc inset;
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  max-width: 700px;
}

.omari-scroll h2 {
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.5rem;
  text-align: center;
  margin-bottom: 0.5rem;
}

.omari-scroll h2 span {
  display: block;
  color: #ff6b35;
  font-size: 1.1rem;
  margin-top: 0.3rem;
  font-family: 'Orbitron', sans-serif;
}

.omari-scroll p {
  color: #ffd700;
  text-align: center;
  font-size: 1rem;
  margin-bottom: 1.2rem;
}

.omari-scroll blockquote {
  color: #ffd700;
  font-style: italic;
  border-left: 3px solid #ff6b35;
  padding-left: 1rem;
  margin: 1.2rem 0;
  background: rgba(30, 16, 40, 0.92);
  border-radius: 8px;
  font-size: 1.08rem;
  line-height: 1.7;
}

.omari-scroll div {
  background: rgba(0,0,0,0.13);
  border-left: 3px solid #00fff7;
  color: #00fff7;
  font-size: 0.98rem;
  padding: 0.7em 1em;
  margin: 0.2em 0 1.2em 0;
  border-radius: 0 0 10px 10px;
  font-style: italic;
}

.omari-scroll div span {
  font-weight: bold;
  color: #ff6b35;
}

.omari-scroll div ul {
  margin: 0.5em 0 0.5em 1.5em;
  padding: 0;
  color: #ffd700;
  font-size: 0.97em;
}

.omari-scroll div li {
  margin-bottom: 0.2em;
}

.glyph-list {
  margin: 1.2em 0 1.2em 1.2em;
}

.glyph-item {
  margin-bottom: 0.7em;
  font-size: 1.08rem;
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
}

.glyph-name {
  font-weight: bold;
  color: #ffd700;
  letter-spacing: 0.05em;
}

.glyph-meaning {
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  font-size: 1rem;
  font-style: normal;
}

.realms-list {
  margin: 2em 0;
  display: flex;
  flex-direction: column;
  gap: 1.2em;
}

.realm-card {
  background: rgba(30, 16, 40, 0.85);
  border-left: 4px solid #00fff7;
  border-radius: 8px;
  padding: 1em 1.2em;
  margin-bottom: 0.5em;
  box-shadow: 0 0 8px #00fff7aa inset;
}

.realm-glyph {
  font-size: 1.2em;
  color: #ffd700;
  font-family: 'Orbitron', sans-serif;
  margin-bottom: 0.2em;
}

.realm-title {
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.15em;
  margin-bottom: 0.1em;
}

.realm-subtitle {
  color: #ff6b35;
  font-size: 1em;
  font-family: 'Orbitron', sans-serif;
  margin-bottom: 0.2em;
}

.realm-description {
  color: #e6e6ff;
  font-size: 0.99em;
  font-family: 'Merriweather', serif;
}

.ghost-language-layers {
  margin: 1.5em 0 1.5em 1em;
  display: flex;
  flex-direction: column;
  gap: 1em;
}

.layer {
  background: rgba(30, 16, 40, 0.85);
  border-left: 4px solid #00fff7;
  border-radius: 8px;
  padding: 0.8em 1em;
  margin-bottom: 0.5em;
  box-shadow: 0 0 8px #00fff7aa inset;
}

.layer-title {
  color: #ff6b35;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.08em;
  margin-bottom: 0.2em;
}

.layer ul {
  margin: 0.5em 0 0.5em 1.2em;
  color: #ffd700;
  font-size: 0.97em;
}

.layer li {
  margin-bottom: 0.2em;
}

.omari-book-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

.omari-book-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.omari-book-header h1 {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.2rem;
  color: #00fff7;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
}

.omari-book-header h2 {
  color: #ff6b35;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.omari-book-intro {
  color: #ffd700;
  font-style: italic;
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1rem;
}

.omari-scroll-selector {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 1.5rem 0;
  flex-wrap: wrap;
}

.omari-scroll-button {
  background-color: rgba(30, 16, 40, 0.8);
  color: #00fff7;
  border: 1px solid #00fff7;
  padding: 0.5rem 1rem;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.omari-scroll-button:hover {
  background-color: rgba(0, 255, 247, 0.1);
  box-shadow: 0 0 10px rgba(0, 255, 247, 0.5);
}

.omari-scroll-button.active {
  background-color: rgba(0, 255, 247, 0.2);
  box-shadow: 0 0 15px rgba(0, 255, 247, 0.7);
  border-color: #ff6b35;
  color: #ff6b35;
}

.omari-book-navigation {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.omari-nav-button {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-family: 'Orbitron', sans-serif;
  font-weight: bold;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  background-color: transparent;
  color: #00fff7;
  border: 2px solid #00fff7;
  box-shadow: 0 0 15px #00fff755;
}

.omari-nav-button:hover {
  background-color: #00fff722;
  box-shadow: 0 0 25px #00fff7aa;
}

.omari-book-footer {
  text-align: center;
  margin-top: 3rem;
}

.omari-book-footer p {
  color: #ffd700;
  font-style: italic;
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2rem;
}

/* Table of Covenants styles */
.border-flame {
  border-color: #ff6b35;
}

.text-flame {
  color: #ff6b35;
}

.text-orange-400 {
  color: #fb923c;
}

@media (max-width: 600px) {
  .omari-scroll {
    padding: 1rem 0.3rem;
  }
  .omari-scroll h2 {
    font-size: 1.1rem;
  }
  .omari-scroll blockquote {
    font-size: 1rem;
    padding-left: 0.5rem;
  }
  .omari-book-header h1 {
    font-size: 1.8rem;
  }
  .glyph-list {
    margin-left: 0.3em;
  }
  .glyph-item {
    font-size: 1rem;
  }
  .glyph-meaning {
    font-size: 0.9rem;
  }
  .realm-card {
    padding: 0.7em 0.5em;
  }
  .realm-glyph {
    font-size: 1.1em;
  }
  .realm-title {
    font-size: 1em;
  }
  .realm-subtitle {
    font-size: 0.9em;
  }
  .realm-description {
    font-size: 0.9em;
  }
  .ghost-language-layers {
    margin-left: 0.2em;
  }
  .layer {
    padding: 0.6em 0.5em;
  }
  .layer-title {
    font-size: 1em;
  }
  .layer ul {
    margin-left: 0.8em;
    font-size: 0.9em;
  }
}
