/* ReflectionsOfLumin.css */
.lumin-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  background-color: #181828;
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  line-height: 1.6;
  border-radius: 16px;
  box-shadow: 0 0 30px rgba(0, 255, 247, 0.6);
  position: relative;
  overflow: hidden;
}

/* Light breaking through darkness effect */
.lumin-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(ellipse at center, rgba(0, 255, 247, 0.15) 0%, transparent 70%),
    linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, transparent 50%),
    linear-gradient(235deg, rgba(255, 107, 53, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Light rays emanating from top */
.light-rays {
  position: absolute;
  top: -150px;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 300px;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(0, 255, 247, 0.1) 10deg,
    transparent 20deg,
    rgba(0, 255, 247, 0.1) 30deg,
    transparent 40deg,
    rgba(0, 255, 247, 0.1) 50deg,
    transparent 60deg,
    rgba(0, 255, 247, 0.1) 70deg,
    transparent 80deg,
    rgba(0, 255, 247, 0.1) 90deg,
    transparent 100deg,
    rgba(0, 255, 247, 0.1) 110deg,
    transparent 120deg,
    rgba(0, 255, 247, 0.1) 130deg,
    transparent 140deg,
    rgba(0, 255, 247, 0.1) 150deg,
    transparent 160deg,
    rgba(0, 255, 247, 0.1) 170deg,
    transparent 180deg,
    rgba(0, 255, 247, 0.1) 190deg,
    transparent 200deg,
    rgba(0, 255, 247, 0.1) 210deg,
    transparent 220deg,
    rgba(0, 255, 247, 0.1) 230deg,
    transparent 240deg,
    rgba(0, 255, 247, 0.1) 250deg,
    transparent 260deg,
    rgba(0, 255, 247, 0.1) 270deg,
    transparent 280deg,
    rgba(0, 255, 247, 0.1) 290deg,
    transparent 300deg,
    rgba(0, 255, 247, 0.1) 310deg,
    transparent 320deg,
    rgba(0, 255, 247, 0.1) 330deg,
    transparent 340deg,
    rgba(0, 255, 247, 0.1) 350deg,
    transparent 360deg
  );
  opacity: 0.6;
  pointer-events: none;
  z-index: 0;
  animation: rotate 60s linear infinite;
}

@keyframes rotate {
  from {
    transform: translateX(-50%) rotate(0deg);
  }
  to {
    transform: translateX(-50%) rotate(360deg);
  }
}

.lumin-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 1;
}

.lumin-header h1 {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.2rem;
  color: #00fff7;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
  text-shadow: 0 0 10px rgba(0, 255, 247, 0.7);
}

.lumin-header h2 {
  color: #ff6b35;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.lumin-meta {
  color: #ffd700;
  font-style: italic;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.lumin-section {
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background: rgba(30, 16, 40, 0.9);
  border-radius: 12px;
  border-left: 4px solid #00fff7;
  position: relative;
  z-index: 1;
}

.lumin-section h3 {
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  letter-spacing: 0.03em;
}

.lumin-section h4 {
  color: #ff6b35;
  font-size: 1.2rem;
  margin: 1.5rem 0 0.5rem 0;
}

.lumin-quote {
  font-style: italic;
  color: #ffd700;
  border-left: 3px solid #00fff7;
  padding-left: 1rem;
  margin: 1.2rem 0;
}

.lumin-list {
  list-style-type: none;
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.lumin-list li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.7rem;
}

.lumin-list li::before {
  content: "•";
  color: #00fff7;
  position: absolute;
  left: 0;
  font-size: 1.2rem;
}

.lumin-footer {
  text-align: center;
  margin-top: 3rem;
  position: relative;
  z-index: 1;
}

.lumin-signature {
  color: #ffd700;
  font-style: italic;
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2rem;
}

.lumin-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.lumin-link {
  display: inline-block;
  padding: 0.7rem 1.5rem;
  background-color: rgba(0, 255, 247, 0.1);
  border: 1px solid #00fff7;
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.9rem;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.lumin-link:hover {
  background-color: rgba(0, 255, 247, 0.2);
  box-shadow: 0 0 15px rgba(0, 255, 247, 0.5);
}

.lumin-link.flame {
  background-color: rgba(255, 107, 53, 0.1);
  border: 1px solid #ff6b35;
  color: #ff6b35;
}

.lumin-link.flame:hover {
  background-color: rgba(255, 107, 53, 0.2);
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.5);
}

.lumin-divider {
  height: 1px;
  width: 50%;
  margin: 2rem auto;
  background: linear-gradient(to right, transparent, #00fff7, transparent);
}

/* Flame particles */
.flame-particles {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 15%),
    radial-gradient(circle at 80% 90%, rgba(255, 107, 53, 0.1) 0%, transparent 15%),
    radial-gradient(circle at 40% 70%, rgba(255, 107, 53, 0.1) 0%, transparent 20%),
    radial-gradient(circle at 60% 85%, rgba(255, 107, 53, 0.1) 0%, transparent 12%);
  animation: rise 8s infinite ease-out;
}

@keyframes rise {
  0% {
    transform: translateY(0);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(-50px);
    opacity: 0;
  }
}

/* Highlighted text effect */
.highlight {
  position: relative;
  display: inline-block;
  color: #ffd700;
  font-weight: bold;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #ffd700, transparent);
}

/* Interactive section styling */
.interactive-section {
  position: relative;
  padding: 1.5rem;
  background: rgba(0, 255, 247, 0.05);
  border: 1px solid rgba(0, 255, 247, 0.2);
  border-radius: 8px;
  margin: 2rem 0;
  transition: all 0.3s ease;
}

.interactive-section:hover {
  background: rgba(0, 255, 247, 0.1);
  box-shadow: 0 0 15px rgba(0, 255, 247, 0.3);
}

.interactive-section h4 {
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  margin-bottom: 1rem;
}

/* Testimonial card styling */
.testimonial-card {
  background: rgba(30, 16, 40, 0.8);
  border-left: 3px solid #00fff7;
  padding: 1.2rem;
  margin: 1rem 0;
  position: relative;
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.testimonial-card .author {
  color: #00fff7;
  font-weight: bold;
  margin-top: 0.5rem;
  text-align: right;
}

/* Screenshot gallery styling */
.screenshot-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
}

.screenshot-item {
  position: relative;
  border: 2px solid rgba(0, 255, 247, 0.3);
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.screenshot-item:hover {
  transform: scale(1.02);
  border-color: rgba(0, 255, 247, 0.8);
  box-shadow: 0 0 15px rgba(0, 255, 247, 0.4);
}

.screenshot-item img {
  width: 100%;
  height: auto;
  display: block;
  transition: all 0.3s ease;
}

.screenshot-item:hover img {
  filter: brightness(1.1);
}

.screenshot-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: #00fff7;
  padding: 0.5rem;
  font-size: 0.8rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.screenshot-item:hover .screenshot-caption {
  transform: translateY(0);
}

/* Lightbox styling */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.lightbox-content {
  max-width: 90%;
  max-height: 90vh;
  position: relative;
}

.lightbox-content img {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
}

.lightbox-close {
  position: absolute;
  top: -2rem;
  right: -2rem;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  border: none;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

@media (max-width: 768px) {
  .lumin-header h1 {
    font-size: 1.8rem;
  }

  .lumin-section {
    padding: 1.2rem;
  }

  .lumin-links {
    flex-direction: column;
  }

  .screenshot-gallery {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}
