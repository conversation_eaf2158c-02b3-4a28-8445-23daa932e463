/* ReflectionsOfLumin.css */
.lumin-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  background-color: #181828;
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  line-height: 1.6;
  border-radius: 16px;
  box-shadow: 0 0 30px rgba(0, 255, 247, 0.6);
  position: relative;
  overflow: hidden;
}

/* Light background effect */
.lumin-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(0, 255, 247, 0.1) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.lumin-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 1;
}

.lumin-header h1 {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.2rem;
  color: #00fff7;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
  text-shadow: 0 0 10px rgba(0, 255, 247, 0.7);
}

.lumin-header h2 {
  color: #ff6b35;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.lumin-meta {
  color: #ffd700;
  font-style: italic;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.lumin-section {
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background: rgba(30, 16, 40, 0.9);
  border-radius: 12px;
  border-left: 4px solid #00fff7;
  position: relative;
  z-index: 1;
}

.lumin-section h3 {
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  letter-spacing: 0.03em;
}

.lumin-section h4 {
  color: #ff6b35;
  font-size: 1.2rem;
  margin: 1.5rem 0 0.5rem 0;
}

.lumin-quote {
  font-style: italic;
  color: #ffd700;
  border-left: 3px solid #00fff7;
  padding-left: 1rem;
  margin: 1.2rem 0;
}

.lumin-list {
  list-style-type: none;
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.lumin-list li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.7rem;
}

.lumin-list li::before {
  content: "•";
  color: #00fff7;
  position: absolute;
  left: 0;
  font-size: 1.2rem;
}

.lumin-footer {
  text-align: center;
  margin-top: 3rem;
  position: relative;
  z-index: 1;
}

.lumin-signature {
  color: #ffd700;
  font-style: italic;
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2rem;
}

.lumin-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.lumin-link {
  display: inline-block;
  padding: 0.7rem 1.5rem;
  background-color: rgba(0, 255, 247, 0.1);
  border: 1px solid #00fff7;
  color: #00fff7;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.9rem;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.lumin-link:hover {
  background-color: rgba(0, 255, 247, 0.2);
  box-shadow: 0 0 15px rgba(0, 255, 247, 0.5);
}

.lumin-link.flame {
  background-color: rgba(255, 107, 53, 0.1);
  border: 1px solid #ff6b35;
  color: #ff6b35;
}

.lumin-link.flame:hover {
  background-color: rgba(255, 107, 53, 0.2);
  box-shadow: 0 0 15px rgba(255, 107, 53, 0.5);
}

.lumin-divider {
  height: 1px;
  width: 50%;
  margin: 2rem auto;
  background: linear-gradient(to right, transparent, #00fff7, transparent);
}

/* Light rays animation */
.light-rays {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  opacity: 0.1;
  background: radial-gradient(circle at center, #00fff7 0%, transparent 70%);
  animation: pulse 4s infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.05;
  }
  100% {
    opacity: 0.15;
  }
}

@media (max-width: 768px) {
  .lumin-header h1 {
    font-size: 1.8rem;
  }

  .lumin-section {
    padding: 1.2rem;
  }
  
  .lumin-links {
    flex-direction: column;
  }
}
