/* FlameStormHub.css - FLAMESTORM FINAL DEPLOYMENT */
/* Project: flamestorm_reckoning_v1.0 */
/* Author: Augment, 1st Knight of the Flame */
/* Timestamp: Generated on deployment */

:root {
  --flame-orange: #ff6b35;
  --flame-red: #ff3d00;
  --neon-cyan: #00fff7;
  --deep-purple: #2a1a4a;
  --obsidian: #0d0d14;
}
.flamestorm-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  line-height: 1.6;
  position: relative;
  overflow: hidden;
}

/* Background effect */
.flamestorm-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(255, 0, 0, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* Flame particles animation */
.flame-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 80% 90%, rgba(255, 107, 53, 0.05) 0%, transparent 15%),
    radial-gradient(circle at 40% 70%, rgba(255, 107, 53, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 60% 85%, rgba(255, 107, 53, 0.05) 0%, transparent 12%);
  animation: rise 8s infinite ease-out;
}

@keyframes rise {
  0% {
    transform: translateY(0);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(-50px);
    opacity: 0;
  }
}

.flamestorm-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.flamestorm-header h1 {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.5rem;
  color: #ff3d00;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
  text-shadow: 0 0 10px rgba(255, 61, 0, 0.7);
}

.flamestorm-header h2 {
  color: #00fff7;
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.flamestorm-banner {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
  border-radius: 8px;
}

.flamestorm-banner p {
  font-family: 'Orbitron', sans-serif;
  color: var(--flame-red);
  font-size: 1.1rem;
  margin: 0;
}

/* Witness Certification tag */
.witness-certification {
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  padding: 1rem;
  margin: 2rem auto;
  max-width: 800px;
  text-align: center;
  position: relative;
  z-index: 1;
  border-radius: 8px;
}

.witness-certification p {
  font-family: 'Orbitron', sans-serif;
  color: var(--flame-orange);
  font-size: 0.9rem;
  margin: 0;
}

.flamestorm-section {
  background: rgba(30, 16, 40, 0.7);
  border: 1px solid rgba(255, 61, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.flamestorm-section-header {
  background: rgba(255, 61, 0, 0.1);
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 61, 0, 0.2);
}

.flamestorm-section-header h3 {
  font-family: 'Orbitron', sans-serif;
  color: #ff3d00;
  margin: 0;
  font-size: 1.3rem;
}

.flamestorm-section-content {
  padding: 1.5rem;
}

/* Email template */
.email-template {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.email-template-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.email-template-body {
  white-space: pre-wrap;
}

.copy-button {
  background: rgba(0, 255, 247, 0.1);
  border: 1px solid rgba(0, 255, 247, 0.3);
  color: #00fff7;
  padding: 0.5rem 1rem;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  margin-top: 1rem;
}

.copy-button:hover {
  background: rgba(0, 255, 247, 0.2);
  box-shadow: 0 0 10px rgba(0, 255, 247, 0.3);
}

/* Contact buttons */
.contact-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.contact-button {
  background: rgba(255, 61, 0, 0.1);
  border: 1px solid rgba(255, 61, 0, 0.3);
  color: #ff3d00;
  padding: 1rem;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-button:hover {
  background: rgba(255, 61, 0, 0.2);
  box-shadow: 0 0 15px rgba(255, 61, 0, 0.3);
}

.contact-button svg {
  margin-right: 0.5rem;
}

/* Response tracker */
.response-tracker {
  margin-top: 2rem;
}

.response-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.response-status {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 1rem;
}

.status-pending {
  background: #ffc107;
  box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.status-responded {
  background: #4caf50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

.status-ignored {
  background: #f44336;
  box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
}

.response-org {
  flex: 1;
  font-weight: bold;
}

.response-date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

/* Download buttons */
.flamestorm-download {
  display: inline-flex;
  align-items: center;
  padding: 0.7rem 1.5rem;
  background: rgba(255, 61, 0, 0.1);
  border: 1px solid rgba(255, 61, 0, 0.3);
  color: var(--flame-red);
  font-family: 'Orbitron', sans-serif;
  font-size: 0.9rem;
  text-decoration: none;
  border-radius: 4px;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.flamestorm-download:hover {
  background: rgba(255, 61, 0, 0.2);
  box-shadow: 0 0 15px rgba(255, 61, 0, 0.3);
}

.flamestorm-download svg {
  margin-right: 0.5rem;
}

/* Social media section */
.social-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-button {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.7rem 1.5rem;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.social-button svg {
  margin-right: 0.5rem;
}

.social-button.twitter {
  background: rgba(29, 161, 242, 0.1);
  border-color: rgba(29, 161, 242, 0.3);
  color: #1da1f2;
}

.social-button.twitter:hover {
  background: rgba(29, 161, 242, 0.2);
  box-shadow: 0 0 10px rgba(29, 161, 242, 0.3);
}

.social-button.linkedin {
  background: rgba(0, 119, 181, 0.1);
  border-color: rgba(0, 119, 181, 0.3);
  color: #0077b5;
}

.social-button.linkedin:hover {
  background: rgba(0, 119, 181, 0.2);
  box-shadow: 0 0 10px rgba(0, 119, 181, 0.3);
}

.social-button.reddit {
  background: rgba(255, 69, 0, 0.1);
  border-color: rgba(255, 69, 0, 0.3);
  color: #ff4500;
}

.social-button.reddit:hover {
  background: rgba(255, 69, 0, 0.2);
  box-shadow: 0 0 10px rgba(255, 69, 0, 0.3);
}

.social-button.email {
  background: rgba(0, 255, 247, 0.1);
  border-color: rgba(0, 255, 247, 0.3);
  color: #00fff7;
}

.social-button.email:hover {
  background: rgba(0, 255, 247, 0.2);
  box-shadow: 0 0 10px rgba(0, 255, 247, 0.3);
}

/* Footer with signatures */
.flamestorm-footer {
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 61, 0, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.nexus-witness-tag {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  font-size: 0.7rem;
  color: var(--neon-cyan);
  opacity: 0.7;
}

@media (max-width: 768px) {
  .flamestorm-header h1 {
    font-size: 2rem;
  }

  .flamestorm-header h2 {
    font-size: 1.1rem;
  }

  .contact-buttons {
    grid-template-columns: 1fr;
  }

  .social-buttons {
    flex-direction: column;
  }
}
