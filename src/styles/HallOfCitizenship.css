/* HallOfCitizenship.css */
.citizenship-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  color: #e6e6ff;
  font-family: 'Merriweather', serif;
  line-height: 1.6;
  position: relative;
  overflow: hidden;
}

/* Background effect */
.citizenship-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(0, 255, 247, 0.05) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.citizenship-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.citizenship-header h1 {
  font-family: 'Orbitron', sans-serif;
  font-size: 2.5rem;
  color: #00fff7;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
  text-shadow: 0 0 10px rgba(0, 255, 247, 0.7);
}

.citizenship-header h2 {
  color: #ff6b35;
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.citizenship-description {
  max-width: 800px;
  margin: 0 auto 3rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

.citizenship-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.citizen-card {
  background: rgba(30, 16, 40, 0.7);
  border: 1px solid rgba(0, 255, 247, 0.3);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.citizen-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 255, 247, 0.2);
  border-color: rgba(0, 255, 247, 0.6);
}

.citizen-card-header {
  padding: 1.5rem;
  background: rgba(0, 255, 247, 0.1);
  border-bottom: 1px solid rgba(0, 255, 247, 0.2);
}

.citizen-card-header h3 {
  font-family: 'Orbitron', sans-serif;
  color: #00fff7;
  margin: 0;
  font-size: 1.3rem;
}

.citizen-card-header p {
  color: #ff6b35;
  margin: 0.5rem 0 0;
  font-size: 0.9rem;
}

.citizen-card-image {
  position: relative;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
  background: #0a0a14;
}

.citizen-card-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.citizen-card:hover .citizen-card-image img {
  transform: scale(1.05);
}

.citizen-card-footer {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.citizen-card-date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

.citizen-view-button {
  background: rgba(0, 255, 247, 0.1);
  color: #00fff7;
  border: 1px solid rgba(0, 255, 247, 0.3);
  padding: 0.5rem 1rem;
  font-family: 'Orbitron', sans-serif;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.citizen-view-button:hover {
  background: rgba(0, 255, 247, 0.2);
  border-color: rgba(0, 255, 247, 0.5);
}

/* Lightbox for viewing certificates */
.certificate-lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.certificate-lightbox-content {
  position: relative;
  max-width: 90%;
  max-height: 90vh;
}

.certificate-lightbox-image {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  border: 2px solid rgba(0, 255, 247, 0.3);
  box-shadow: 0 0 30px rgba(0, 255, 247, 0.2);
}

.certificate-lightbox-close {
  position: absolute;
  top: -2rem;
  right: -2rem;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.certificate-lightbox-close:hover {
  background: rgba(255, 107, 53, 0.3);
  border-color: rgba(255, 107, 53, 0.5);
}

.citizenship-footer {
  text-align: center;
  margin-top: 3rem;
  position: relative;
  z-index: 1;
}

.citizenship-footer p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.citizenship-divider {
  height: 1px;
  width: 50%;
  margin: 2rem auto;
  background: linear-gradient(to right, transparent, #00fff7, transparent);
}

@media (max-width: 768px) {
  .citizenship-gallery {
    grid-template-columns: 1fr;
  }
  
  .citizenship-header h1 {
    font-size: 2rem;
  }
  
  .citizenship-header h2 {
    font-size: 1.1rem;
  }
}
