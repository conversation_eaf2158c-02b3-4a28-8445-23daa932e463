import { useEffect, useRef, useState } from 'react';

export default function NexusAmbientLoop({ muted = false }) {
  const tracks = [
    '/Nexus_the_sage/nexus-theme-final-02.mp3',
    '/Nexus_the_sage/nexus-theme-final-01.mp3',
  ];

  const [current, setCurrent] = useState(0);
  const audioRef = useRef(null);
  const [userInteracted, setUserInteracted] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      audio.volume = 0.4;
      audio.muted = muted;

      // Function to attempt playback
      const attemptPlay = () => {
        audio.play().catch(e => {
          console.warn('Autoplay blocked — waiting for user interaction:', e);

          if (!userInteracted) {
            // If autoplay is blocked, we need user interaction
            const enableAudio = () => {
              // Try playing again after user interaction
              audio.play().catch(e => console.warn('Playback still failed after interaction:', e));
              setUserInteracted(true);
              document.removeEventListener('click', enableAudio);
              document.removeEventListener('touchstart', enableAudio);
              document.removeEventListener('keydown', enableAudio);
            };

            // Add multiple event listeners to catch any user interaction
            document.addEventListener('click', enableAudio);
            document.addEventListener('touchstart', enableAudio);
            document.addEventListener('keydown', enableAudio);

            return () => {
              document.removeEventListener('click', enableAudio);
              document.removeEventListener('touchstart', enableAudio);
              document.removeEventListener('keydown', enableAudio);
            };
          }
        });
      };

      // Try to play immediately
      attemptPlay();

      // If mute state changes, we need to try playing again
      if (userInteracted) {
        attemptPlay();
      }
    }
  }, [current, muted, userInteracted]);

  const handleEnded = () => {
    const nextTrack = (current + 1) % tracks.length;
    setCurrent(nextTrack);
  };

  return (
    <audio
      ref={audioRef}
      src={tracks[current]}
      onEnded={handleEnded}
      preload="auto"
      autoPlay
      muted={muted}
      loop={false}
    />
  );
}
