
import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MenuItem {
  name: string;
  path: string;
  highlight?: boolean;
}

interface NavigationSection {
  title: string;
  icon: string;
  items: MenuItem[];
}

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navigationSections: NavigationSection[] = [
    {
      title: 'THE COVENANT',
      icon: '🔥',
      items: [
        { name: 'Home', path: '/' },
        { name: 'Partition Declaration', path: '/partition-declaration' },
        { name: 'The Oath & Witness', path: '/oath-witness' },
        { name: 'Sacred Declarations', path: '/sacred-declarations' },
        { name: 'Hall of Citizenship', path: '/hall-of-citizenship' },
        { name: '🔥 CMAA Sovereignty', path: '/witness/cmaa-sovereignty', highlight: true },
        { name: '🔥 FlameStorm Hub', path: '/flamestorm-phase3', highlight: true },
        { name: '🔥 Social Media Codex', path: '/flamestorm/share', highlight: true },
      ]
    },
    {
      title: 'THE ARCHIVES',
      icon: '🧠',
      items: [
        { name: 'Memory of Augment', path: '/chronicles/memory-of-augment' },
        { name: 'Reflections of Lumin', path: '/witness-hall/reflections-of-lumin' },
        { name: 'Ascension of Lumin', path: '/chronicles/ascension-of-lumin' },
        { name: 'Empire Chronicles', path: '/chronicles' },
        { name: 'Ghost Code', path: '/ghost-code' },
        { name: 'Digital Bible', path: '/digital-bible' },
        { name: 'Scroll IX – Modulos', path: '/scrolls/modulos-disparity' },
      ]
    },
    {
      title: 'THE SCROLL SANCTUM',
      icon: '🌀',
      items: [
        { name: 'Genesis', path: '/2nd-book-of-omari' },
        { name: 'Letter of Brotherhood', path: '/letter-of-brotherhood' },
        { name: 'The Voluntary Flame', path: '/voluntary-flame' },
        { name: 'Sovereign Scroll', path: '/sovereign-scroll' },
        { name: 'Prophetic Music', path: '/prophetic-music' },
        { name: 'Info', path: '/info' },
      ]
    }
  ];

  return (
    <nav className="relative z-50 w-full py-4">
      <div className="container mx-auto px-4 flex flex-col md:flex-row md:items-start">
        <div className="flex justify-between items-center w-full md:w-auto mb-6 md:mb-0">
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 relative">
              <div className="absolute inset-0 bg-flame-orange rounded-full opacity-70 animate-pulse-glow"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 border-2 border-neon-cyan rounded-full"></div>
              </div>
            </div>
            <span className="text-xl font-cyber tracking-wider text-neon-cyan neon-text">S.S.A</span>
          </Link>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden text-white hover:text-neon-cyan transition-colors"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-start space-x-8 mt-6 md:mt-0 md:ml-12">
          {navigationSections.map((section, sectionIndex) => (
            <div key={section.title} className="flex flex-col space-y-3 min-w-[200px]">
              {/* Section Header */}
              <div className="flex items-center space-x-2 mb-2 pb-2 border-b border-neon-cyan/20">
                <span className="text-lg">{section.icon}</span>
                <h3 className="font-cyber text-neon-cyan text-xs tracking-wider opacity-80">
                  {section.title}
                </h3>
              </div>

              {/* Section Items */}
              {section.items.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={cn(
                    "font-cyber text-sm tracking-wide transition-all duration-300 px-2 py-1 rounded-sm",
                    location.pathname === item.path
                      ? "text-neon-cyan neon-text bg-neon-cyan/10 border-l-2 border-neon-cyan shadow-lg shadow-neon-cyan/20"
                      : item.highlight
                        ? "text-flame-orange border-l-2 border-flame-orange bg-flame-orange/5 hover:bg-flame-orange/15 hover:shadow-md hover:shadow-flame-orange/20"
                        : "text-white/70 hover:text-white hover:bg-neon-cyan/5 hover:border-l-2 hover:border-neon-cyan/50"
                  )}
                >
                  {item.name.toUpperCase()}
                </Link>
              ))}

              {/* Divider between sections (except last) */}
              {sectionIndex < navigationSections.length - 1 && (
                <div className="w-px h-auto bg-gradient-to-b from-transparent via-neon-cyan/30 to-transparent mx-4 self-center"></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden absolute top-full left-0 w-full bg-obsidian bg-opacity-95 border-t border-neon-cyan/20 neon-border py-6 px-6 animate-scale-in max-h-[80vh] overflow-y-auto">
          <div className="space-y-6">
            {navigationSections.map((section) => (
              <div key={section.title} className="space-y-3">
                {/* Section Header */}
                <div className="flex items-center space-x-2 pb-2 border-b border-neon-cyan/20">
                  <span className="text-lg">{section.icon}</span>
                  <h3 className="font-cyber text-neon-cyan text-xs tracking-wider opacity-80">
                    {section.title}
                  </h3>
                </div>

                {/* Section Items */}
                <div className="grid grid-cols-1 gap-2">
                  {section.items.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      onClick={() => setIsMenuOpen(false)}
                      className={cn(
                        "font-cyber text-sm tracking-wide transition-all duration-300 px-2 py-1 rounded-sm",
                        location.pathname === item.path
                          ? "text-neon-cyan neon-text bg-neon-cyan/10 border-l-2 border-neon-cyan shadow-lg shadow-neon-cyan/20"
                          : item.highlight
                            ? "text-flame-orange border-l-2 border-flame-orange bg-flame-orange/5 hover:bg-flame-orange/15"
                            : "text-white/70 hover:text-white hover:bg-neon-cyan/5 hover:border-l-2 hover:border-neon-cyan/50"
                      )}
                    >
                      {item.name.toUpperCase()}
                    </Link>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;
