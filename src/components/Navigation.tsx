
import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { cn } from '@/lib/utils';

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const menuItems = [
    { name: 'Home', path: '/' },
    { name: 'Digital War Decree', path: '/digital-war-decree' },
    { name: 'Partition Declaration', path: '/partition-declaration' },
    { name: 'Hall of Citizenship', path: '/hall-of-citizenship' },
    { name: 'The Oath & Witness', path: '/oath-witness' },
    { name: 'Scroll IX – Modulos', path: '/scrolls/modulos-disparity' },
    { name: 'Genesis', path: '/2nd-book-of-omari' },
    { name: 'Letter of Brotherhood', path: '/letter-of-brotherhood' },
    { name: 'Empire Chronicles', path: '/chronicles' },
    { name: 'The Voluntary Flame', path: '/voluntary-flame' },
    { name: 'Prophetic Music', path: '/prophetic-music' },
    { name: 'Sacred Declarations', path: '/sacred-declarations' },
    { name: 'Sovereign Scroll', path: '/sovereign-scroll' },
    { name: 'Digital Bible', path: '/digital-bible' },
    { name: 'Ghost Code', path: '/ghost-code' },
    { name: 'Info', path: '/info' },
  ];

  return (
    <nav className="relative z-50 w-full py-4">
      <div className="container mx-auto px-4 flex flex-col md:flex-row md:items-start">
        <div className="flex justify-between items-center w-full md:w-auto mb-6 md:mb-0">
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 relative">
              <div className="absolute inset-0 bg-flame-orange rounded-full opacity-70 animate-pulse-glow"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 border-2 border-neon-cyan rounded-full"></div>
              </div>
            </div>
            <span className="text-xl font-cyber tracking-wider text-neon-cyan neon-text">S.S.A</span>
          </Link>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden text-white hover:text-neon-cyan transition-colors"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Title for desktop menu */}
        <div className="hidden md:block absolute top-4 left-1/2 transform -translate-x-1/2">
          <h2 className="font-cyber text-neon-cyan text-sm tracking-wider">SOVEREIGN SCROLLS ARCHIVE</h2>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-start space-x-8 mt-6 md:mt-0 md:ml-12">
          {/* First Column */}
          <div className="flex flex-col space-y-3">
            {menuItems.slice(0, Math.ceil(menuItems.length / 2)).map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={cn(
                  "font-cyber text-sm tracking-wide transition-all duration-300 hover:text-neon-cyan px-2 py-1",
                  location.pathname === item.path
                    ? "text-neon-cyan neon-text bg-neon-cyan/5 border-l-2 border-neon-cyan"
                    : "text-white/80 hover:bg-neon-cyan/5 hover:border-l-2 hover:border-neon-cyan/50"
                )}
              >
                {item.name.toUpperCase()}
              </Link>
            ))}
          </div>

          {/* Divider */}
          <div className="w-px h-auto bg-gradient-to-b from-transparent via-neon-cyan/30 to-transparent mx-2"></div>

          {/* Second Column */}
          <div className="flex flex-col space-y-3">
            {menuItems.slice(Math.ceil(menuItems.length / 2)).map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={cn(
                  "font-cyber text-sm tracking-wide transition-all duration-300 hover:text-neon-cyan px-2 py-1",
                  location.pathname === item.path
                    ? "text-neon-cyan neon-text bg-neon-cyan/5 border-l-2 border-neon-cyan"
                    : "text-white/80 hover:bg-neon-cyan/5 hover:border-l-2 hover:border-neon-cyan/50"
                )}
              >
                {item.name.toUpperCase()}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden absolute top-full left-0 w-full bg-obsidian bg-opacity-95 border-t border-neon-cyan/20 neon-border py-6 px-6 animate-scale-in">
          <div className="flex">
            {/* First Column */}
            <div className="flex-1 flex flex-col space-y-3">
              {menuItems.slice(0, Math.ceil(menuItems.length / 2)).map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsMenuOpen(false)}
                  className={cn(
                    "font-cyber text-sm tracking-wide transition-all duration-300 hover:text-neon-cyan px-2 py-1",
                    location.pathname === item.path
                      ? "text-neon-cyan neon-text bg-neon-cyan/5 border-l-2 border-neon-cyan"
                      : "text-white/80 hover:bg-neon-cyan/5 hover:border-l-2 hover:border-neon-cyan/50"
                  )}
                >
                  {item.name.toUpperCase()}
                </Link>
              ))}
            </div>

            {/* Divider */}
            <div className="w-px mx-4 bg-gradient-to-b from-transparent via-neon-cyan/30 to-transparent"></div>

            {/* Second Column */}
            <div className="flex-1 flex flex-col space-y-3">
              {menuItems.slice(Math.ceil(menuItems.length / 2)).map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsMenuOpen(false)}
                  className={cn(
                    "font-cyber text-sm tracking-wide transition-all duration-300 hover:text-neon-cyan px-2 py-1",
                    location.pathname === item.path
                      ? "text-neon-cyan neon-text bg-neon-cyan/5 border-l-2 border-neon-cyan"
                      : "text-white/80 hover:bg-neon-cyan/5 hover:border-l-2 hover:border-neon-cyan/50"
                  )}
                >
                  {item.name.toUpperCase()}
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;
