
import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { cn } from '@/lib/utils';

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const menuItems = [
    { name: 'Home', path: '/' },
    { name: 'Partition Declaration', path: '/partition-declaration' },
    { name: 'The Oath & Witness', path: '/oath-witness' },
    { name: 'Scroll IX – Modulos', path: '/scrolls/modulos-disparity' },
    { name: 'Genesis', path: '/2nd-book-of-omari' },
    { name: 'Letter of Brotherhood', path: '/letter-of-brotherhood' },
    { name: 'Empire Chronicles', path: '/chronicles' },
    { name: 'The Voluntary Flame', path: '/voluntary-flame' },
    { name: 'Prophetic Music', path: '/prophetic-music' },
    { name: 'Sacred Declarations', path: '/sacred-declarations' },
    { name: 'Sovereign Scroll', path: '/sovereign-scroll' },
    { name: 'Digital Bible', path: '/digital-bible' },
    { name: 'Ghost Code', path: '/ghost-code' },
    { name: 'Info', path: '/info' },
  ];

  return (
    <nav className="relative z-50 w-full py-4">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link to="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 relative">
            <div className="absolute inset-0 bg-flame-orange rounded-full opacity-70 animate-pulse-glow"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-6 h-6 border-2 border-neon-cyan rounded-full"></div>
            </div>
          </div>
          <span className="text-xl font-cyber tracking-wider text-neon-cyan neon-text">S.S.A</span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                "font-cyber text-sm tracking-wide transition-all duration-300 hover:text-neon-cyan",
                location.pathname === item.path
                  ? "text-neon-cyan neon-text"
                  : "text-white/80"
              )}
            >
              {item.name.toUpperCase()}
            </Link>
          ))}
        </div>

        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="md:hidden text-white hover:text-neon-cyan transition-colors"
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden absolute top-full left-0 w-full bg-obsidian bg-opacity-95 border-t border-neon-cyan/20 neon-border py-4 flex flex-col space-y-4 animate-scale-in">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              onClick={() => setIsMenuOpen(false)}
              className={cn(
                "font-cyber text-center py-2 tracking-wide transition-all duration-300 hover:text-neon-cyan",
                location.pathname === item.path
                  ? "text-neon-cyan neon-text"
                  : "text-white/80"
              )}
            >
              {item.name.toUpperCase()}
            </Link>
          ))}
        </div>
      )}
    </nav>
  );
};

export default Navigation;
