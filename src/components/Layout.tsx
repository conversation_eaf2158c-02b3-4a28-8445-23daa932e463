import { ReactNode } from 'react';
import Navigation from './Navigation';
import { AudioControlButton } from '../context/AudioContext';

interface LayoutProps {
  children: ReactNode;
  className?: string;
  watermark?: boolean;
}

const Layout = ({ children, className = '', watermark = true }: LayoutProps) => {
  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      <Navigation />
      <AudioControlButton />

      <main className="flex-grow">
        {children}
      </main>

      {watermark && (
        <div className="pointer-events-none fixed bottom-4 right-4 z-50 opacity-70 hover:opacity-100 transition-opacity duration-300">
          <img
            src="/NODE_watermark.png"
            alt="GodsIMiJ AI Solutions"
            className="w-24 h-auto"
          />
        </div>
      )}

      <div className="flame-bg h-16 w-full fixed bottom-0 left-0 z-0 pointer-events-none"></div>
    </div>
  );
};

export default Layout;
