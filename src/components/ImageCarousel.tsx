
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { AspectRatio } from "@/components/ui/aspect-ratio";

interface ImageCarouselProps {
  images: string[];
}

const ImageCarousel = ({ images }: ImageCarouselProps) => {
  return (
    <Carousel
      opts={{
        align: "start",
        loop: true,
      }}
      className="w-full max-w-5xl mx-auto"
    >
      <CarouselContent className="-ml-2 md:-ml-4">
        {images.map((image, index) => (
          <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
            <div className="relative rounded-lg overflow-hidden hover:transform hover:scale-[1.02] transition-transform border border-neon-cyan/30 neon-border">
              <AspectRatio ratio={16 / 9}>
                <img
                  src={image}
                  alt={`Sacred Declaration ${index + 1}`}
                  className="object-cover w-full h-full"
                  loading="lazy"
                />
              </AspectRatio>
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious className="hidden md:flex -left-12 hover:text-neon-cyan hover:border-neon-cyan transition-colors" />
      <CarouselNext className="hidden md:flex -right-12 hover:text-neon-cyan hover:border-neon-cyan transition-colors" />
    </Carousel>
  );
};

export default ImageCarousel;
