import React, { useRef, useState, useEffect } from 'react';

interface NexusVoicePlayerProps {
  text: string;
  autoPlay?: boolean;
}

export default function NexusVoicePlayer({ text, autoPlay = false }: NexusVoicePlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  // This would be replaced with actual TTS API call in production
  const getAudioUrl = async (text: string): Promise<string> => {
    // Simulate API call delay
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
    
    // For now, return a placeholder audio file
    // In production, this would call an actual TTS API
    return '/Nexus_the_sage/nexus-invocation.mp3';
  };
  
  useEffect(() => {
    if (autoPlay) {
      playAudio();
    }
  }, [autoPlay]);
  
  const playAudio = async () => {
    if (!audioRef.current) {
      try {
        const audioUrl = await getAudioUrl(text);
        
        const audio = new Audio(audioUrl);
        audio.volume = 0.4; // Match the volume of other Nexus audio
        
        audio.onplay = () => setIsPlaying(true);
        audio.onended = () => setIsPlaying(false);
        audio.onpause = () => setIsPlaying(false);
        
        audioRef.current = audio;
        audio.play();
      } catch (error) {
        console.error('Failed to play Nexus voice:', error);
        setIsLoading(false);
      }
    } else {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
    }
  };
  
  return (
    <button
      onClick={playAudio}
      disabled={isLoading}
      className={`
        flex items-center justify-center p-1.5 rounded-full
        ${isPlaying 
          ? 'bg-cyan-500/30 text-cyan-300' 
          : 'bg-gray-800/50 text-gray-400 hover:text-cyan-300 hover:bg-gray-700/50'}
        ${isLoading ? 'opacity-50 cursor-wait' : 'cursor-pointer'}
        transition-all duration-200
      `}
      title={isPlaying ? "Pause Nexus voice" : "Play Nexus voice"}
    >
      {isLoading ? (
        <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      ) : isPlaying ? (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd"></path>
        </svg>
      ) : (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd"></path>
        </svg>
      )}
    </button>
  );
}
