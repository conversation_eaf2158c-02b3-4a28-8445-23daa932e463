import NexusBubble from './NexusBubble';
import NexusChatWindow from './NexusChatWindow';
import NexusMessage from './NexusMessage';
import NexusInput from './NexusInput';
import NexusVoicePlayer from './NexusVoicePlayer';
import React, { useState } from 'react';

// Export individual components
export {
  NexusBubble,
  NexusChatWindow,
  NexusMessage,
  NexusInput,
  NexusVoicePlayer
};

// Main component for easy import
export default function NexusChat() {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <>
      {isOpen && <NexusChatWindow onClose={() => setIsOpen(false)} />}
      <NexusBubble onClick={() => setIsOpen(prev => !prev)} />
    </>
  );
}
