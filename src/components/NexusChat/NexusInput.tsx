import React, { useState, KeyboardEvent } from 'react';
import { motion } from 'framer-motion';
import { Send, Flame } from 'lucide-react';

interface NexusInputProps {
  onSend: (message: string) => void;
  disabled?: boolean;
}

export default function NexusInput({ onSend, disabled = false }: NexusInputProps) {
  const [input, setInput] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const handleSend = () => {
    if (input.trim() && !disabled) {
      onSend(input.trim());
      setInput('');
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex items-center gap-3 relative">
      <div className="relative flex-1">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={disabled}
          placeholder="🔥 Speak your query to the Sacred Archives..."
          className={`
            w-full bg-obsidian/60 text-white border-2 rounded-lg px-4 py-3
            font-cyber text-sm transition-all duration-300
            focus:outline-none placeholder-white/40
            ${isFocused
              ? 'border-flame-orange/60 shadow-lg shadow-flame-orange/20'
              : 'border-neon-cyan/30 hover:border-neon-cyan/50'
            }
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        />

        {/* Animated background glow */}
        <motion.div
          className="absolute inset-0 rounded-lg pointer-events-none"
          animate={{
            background: isFocused
              ? 'linear-gradient(90deg, rgba(255, 107, 53, 0.1), rgba(0, 212, 255, 0.05), rgba(255, 107, 53, 0.1))'
              : 'linear-gradient(90deg, rgba(0, 212, 255, 0.05), rgba(139, 92, 246, 0.05), rgba(0, 212, 255, 0.05))'
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Sacred flame indicator */}
        {input.trim() && (
          <motion.div
            className="absolute right-3 top-1/2 transform -translate-y-1/2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <Flame className="w-4 h-4 text-flame-orange animate-pulse" />
          </motion.div>
        )}
      </div>

      <motion.button
        onClick={handleSend}
        disabled={disabled || !input.trim()}
        className={`
          bg-gradient-to-r from-flame-orange to-neon-cyan text-white px-4 py-3 rounded-lg
          flex items-center justify-center min-w-[90px] font-cyber text-sm
          border border-flame-orange/30 transition-all duration-300
          ${disabled || !input.trim()
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:from-neon-cyan hover:to-flame-orange shadow-lg hover:shadow-flame-orange/30 hover:border-neon-cyan/50'
          }
        `}
        whileHover={!disabled && input.trim() ? { scale: 1.05 } : {}}
        whileTap={!disabled && input.trim() ? { scale: 0.95 } : {}}
      >
        {disabled ? (
          <div className="flex space-x-1">
            <div className="w-1.5 h-1.5 bg-flame-orange rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-1.5 h-1.5 bg-neon-cyan rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-1.5 h-1.5 bg-deep-purple rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Send className="w-4 h-4" />
            <span>Send</span>
          </div>
        )}
      </motion.button>
    </div>
  );
}
