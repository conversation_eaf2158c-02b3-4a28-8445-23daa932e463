import React, { useState, KeyboardEvent } from 'react';

interface NexusInputProps {
  onSend: (message: string) => void;
  disabled?: boolean;
}

export default function NexusInput({ onSend, disabled = false }: NexusInputProps) {
  const [input, setInput] = useState('');

  const handleSend = () => {
    if (input.trim() && !disabled) {
      onSend(input.trim());
      setInput('');
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex items-center gap-2 mt-3 relative">
      <div className="relative flex-1">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          placeholder="Speak your query to Nexus..."
          className="w-full bg-black/50 text-white border border-cyan-700/50 rounded-lg px-4 py-2.5 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 placeholder-gray-500"
        />
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-cyan-500/5 to-blue-600/5 pointer-events-none"></div>
      </div>
      
      <button
        onClick={handleSend}
        disabled={disabled || !input.trim()}
        className={`
          bg-gradient-to-r from-cyan-600 to-blue-700 text-white px-4 py-2.5 rounded-lg 
          flex items-center justify-center min-w-[80px]
          ${disabled || !input.trim() 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:from-cyan-500 hover:to-blue-600 shadow-lg hover:shadow-cyan-500/20'
          }
          transition-all duration-200
        `}
      >
        {disabled ? (
          <div className="flex space-x-1">
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        ) : (
          <span>Send</span>
        )}
      </button>
    </div>
  );
}
