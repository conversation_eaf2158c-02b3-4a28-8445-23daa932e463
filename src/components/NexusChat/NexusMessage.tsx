import React from 'react';

interface NexusMessageProps {
  content: string;
  isUser: boolean;
  timestamp?: string;
}

export default function NexusMessage({ content, isUser, timestamp }: NexusMessageProps) {
  return (
    <div className={`mb-4 ${isUser ? 'ml-auto' : 'mr-auto'} max-w-[85%]`}>
      <div 
        className={`
          relative p-3 rounded-xl shadow-md 
          ${isUser 
            ? 'bg-gradient-to-br from-flame-orange/20 to-flame-orange/10 border border-flame-orange/30 text-white' 
            : 'bg-gradient-to-br from-cyan-500/20 to-blue-600/10 border border-cyan-400/30 text-cyan-100'
          }
        `}
      >
        <div className="flex items-center mb-1">
          <div 
            className={`
              text-xs font-bold 
              ${isUser ? 'text-flame-orange' : 'text-cyan-300'}
            `}
          >
            {isUser ? 'You' : 'Nexus'}
          </div>
          {timestamp && (
            <div className="text-xs text-gray-400 ml-2">
              {timestamp}
            </div>
          )}
        </div>
        <div className="whitespace-pre-wrap text-sm">{content}</div>
        
        {!isUser && (
          <div className="absolute -left-1 -bottom-1 w-3 h-3 rounded-full bg-cyan-500/50 animate-pulse"></div>
        )}
      </div>
    </div>
  );
}
