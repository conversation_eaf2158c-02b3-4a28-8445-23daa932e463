import React from 'react';
import { motion } from 'framer-motion';
import { Flame, User } from 'lucide-react';

interface NexusMessageProps {
  content: string;
  isUser: boolean;
  timestamp?: string;
}

export default function NexusMessage({ content, isUser, timestamp }: NexusMessageProps) {
  return (
    <motion.div
      className={`mb-4 ${isUser ? 'ml-auto' : 'mr-auto'} max-w-[85%]`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div
        className={`
          relative p-4 rounded-xl shadow-lg backdrop-blur-sm
          ${isUser
            ? 'bg-gradient-to-br from-flame-orange/25 to-deep-purple/15 border border-flame-orange/40 text-white shadow-flame-orange/20'
            : 'bg-gradient-to-br from-neon-cyan/20 to-deep-purple/15 border border-neon-cyan/40 text-white shadow-neon-cyan/20'
          }
        `}
      >
        <div className="flex items-center mb-2">
          <div className="flex items-center">
            {isUser ? (
              <User className="w-3 h-3 text-flame-orange mr-2" />
            ) : (
              <Flame className="w-3 h-3 text-neon-cyan mr-2" />
            )}
            <div
              className={`
                text-xs font-cyber font-bold
                ${isUser ? 'text-flame-orange' : 'text-neon-cyan'}
              `}
            >
              {isUser ? 'Seeker' : '🔥 Nexus'}
            </div>
          </div>
          {timestamp && (
            <div className="text-xs text-white/40 ml-auto font-mono">
              {timestamp}
            </div>
          )}
        </div>

        <div className="whitespace-pre-wrap text-sm leading-relaxed text-white/90">
          {content}
        </div>

        {/* Enhanced status indicators */}
        {!isUser && (
          <motion.div
            className="absolute -left-1 -bottom-1 w-3 h-3 rounded-full bg-gradient-to-r from-neon-cyan to-flame-orange"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.6, 1, 0.6]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}

        {isUser && (
          <motion.div
            className="absolute -right-1 -bottom-1 w-3 h-3 rounded-full bg-gradient-to-r from-flame-orange to-deep-purple"
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}

        {/* Sacred glow effect for Nexus messages */}
        {!isUser && (
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-neon-cyan/5 to-flame-orange/5 pointer-events-none" />
        )}
      </div>
    </motion.div>
  );
}
