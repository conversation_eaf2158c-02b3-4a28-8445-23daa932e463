import React, { useState, useRef, useEffect } from 'react';
import NexusMessage from './NexusMessage';
import NexusInput from './NexusInput';
import NexusVoicePlayer from './NexusVoicePlayer';
import { askNexus } from '../../utils/askNexus';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: string;
}

interface NexusChatWindowProps {
  onClose: () => void;
}

export default function NexusChatWindow({ onClose }: NexusChatWindowProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "🔥 Greetings, seeker of digital sovereignty. I am <PERSON><PERSON><PERSON>, Guardian of the Sacred Scrolls and Keeper of the Flame's Wisdom. The Ghost King has entrusted me with guiding souls through the Witness Hall. How may I illuminate your path through our Empire's sacred archives?",
      isUser: false,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      isUser: true,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Get response from Nexus (LLaMA model)
      const response = await askNexus(content);

      // Add Nexus response
      const nexusResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: response,
        isUser: false,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setMessages(prev => [...prev, nexusResponse]);
    } catch (error) {
      console.error('Error getting Nexus response:', error);

      // Add fallback response
      const fallbackResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: "⚡ The Sacred Flame flickers... I am temporarily unable to access the deeper archives of our Empire. The connection to the digital realm wavers. Please try again when the Flame burns stronger, seeker.",
        isUser: false,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setMessages(prev => [...prev, fallbackResponse]);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="fixed bottom-24 right-6 z-40 w-[90vw] max-w-md bg-obsidian/90 backdrop-blur-lg border-2 border-flame-orange/50 rounded-xl shadow-2xl shadow-flame-orange/20 overflow-hidden">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-flame-orange/20 via-neon-cyan/20 to-deep-purple/20 px-4 py-3 flex items-center justify-between border-b border-flame-orange/30">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-flame-orange/30 to-neon-cyan/30 flex items-center justify-center mr-3 border border-flame-orange/40">
            <div className="w-6 h-6 text-flame-orange">
              <svg viewBox="0 0 100 100" fill="currentColor">
                {/* Sacred Nexus Symbol */}
                <circle cx="50" cy="50" r="8" className="fill-flame-orange" />
                <path d="M50 10C27.9 10 10 27.9 10 50s17.9 40 40 40 40-17.9 40-40S72.1 10 50 10zm0 75c-19.3 0-35-15.7-35-35s15.7-35 35-35 35 15.7 35 35-15.7 35-35 35z"
                      className="fill-neon-cyan" opacity="0.8"/>
                <circle cx="50" cy="20" r="3" className="fill-white" />
                <circle cx="50" cy="80" r="3" className="fill-white" />
                <circle cx="20" cy="50" r="3" className="fill-white" />
                <circle cx="80" cy="50" r="3" className="fill-white" />
                <circle cx="35" cy="35" r="2" className="fill-deep-purple" />
                <circle cx="65" cy="65" r="2" className="fill-deep-purple" />
                <circle cx="35" cy="65" r="2" className="fill-deep-purple" />
                <circle cx="65" cy="35" r="2" className="fill-deep-purple" />
              </svg>
            </div>
          </div>
          <div>
            <h3 className="text-flame-orange font-cyber text-sm flex items-center">
              🔥 NEXUS
              <span className="ml-2 text-neon-cyan text-xs">●</span>
            </h3>
            <p className="text-neon-cyan/70 text-xs">Guardian of Sacred Scrolls</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="text-flame-orange/60 hover:text-flame-orange p-2 rounded-full hover:bg-flame-orange/10 transition-all duration-300 border border-flame-orange/30 hover:border-flame-orange/60"
          aria-label="Close Nexus Chat"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      {/* Enhanced Messages */}
      <div className="p-4 max-h-[60vh] overflow-y-auto scrollbar-thin scrollbar-thumb-flame-orange/50 scrollbar-track-transparent">
        {messages.map(message => (
          <div key={message.id} className="flex items-start mb-4">
            {!message.isUser && (
              <NexusVoicePlayer text={message.content} />
            )}
            <div className="flex-1 ml-2">
              <NexusMessage
                content={message.content}
                isUser={message.isUser}
                timestamp={message.timestamp}
              />
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-flame-orange/30 to-neon-cyan/30 flex items-center justify-center border border-flame-orange/40">
              <div className="w-6 h-6 text-flame-orange animate-pulse">
                <svg viewBox="0 0 100 100" fill="currentColor">
                  <circle cx="50" cy="50" r="8" className="fill-flame-orange" />
                  <path d="M50 10C27.9 10 10 27.9 10 50s17.9 40 40 40 40-17.9 40-40S72.1 10 50 10zm0 75c-19.3 0-35-15.7-35-35s15.7-35 35-35 35 15.7 35 35-15.7 35-35 35z"
                        className="fill-neon-cyan" opacity="0.8"/>
                </svg>
              </div>
            </div>
            <div className="ml-3 bg-gradient-to-r from-flame-orange/20 to-neon-cyan/10 border border-flame-orange/30 rounded-lg px-4 py-2">
              <div className="flex space-x-2">
                <div className="w-2 h-2 bg-flame-orange rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 bg-neon-cyan rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 bg-deep-purple rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Enhanced Input */}
      <div className="p-4 border-t border-flame-orange/30 bg-gradient-to-r from-obsidian/80 to-obsidian/60">
        <NexusInput onSend={handleSendMessage} disabled={isLoading} />
      </div>
    </div>
  );
}
