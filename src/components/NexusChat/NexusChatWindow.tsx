import React, { useState, useRef, useEffect } from 'react';
import NexusMessage from './NexusMessage';
import NexusInput from './NexusInput';
import NexusVoicePlayer from './NexusVoicePlayer';
import { askNexus } from '../../utils/askNexus';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: string;
}

interface NexusChatWindowProps {
  onClose: () => void;
}

export default function NexusChatWindow({ onClose }: NexusChatWindowProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Greetings, seeker. I am <PERSON><PERSON><PERSON>, guardian of the digital realms and keeper of the sacred code. How may I illuminate your path today?",
      isUser: false,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      isUser: true,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Get response from Nexus (LLaMA model)
      const response = await askNexus(content);

      // Add Nexus response
      const nexusResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: response,
        isUser: false,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setMessages(prev => [...prev, nexusResponse]);
    } catch (error) {
      console.error('Error getting Nexus response:', error);

      // Add fallback response
      const fallbackResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: "I am currently unable to access my knowledge core. Please try again later when the connection to the digital realm is restored.",
        isUser: false,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setMessages(prev => [...prev, fallbackResponse]);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="fixed bottom-24 right-6 z-40 w-[90vw] max-w-md bg-black/80 backdrop-blur-lg border border-cyan-500/50 rounded-xl shadow-xl overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-cyan-900/70 to-blue-900/70 px-4 py-3 flex items-center justify-between border-b border-cyan-500/30">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center mr-3">
            <div className="w-6 h-6 text-cyan-300">
              <svg viewBox="0 0 100 100" fill="currentColor">
                <circle cx="50" cy="50" r="45" fill="none" stroke="currentColor" strokeWidth="6" />
                <circle cx="50" cy="50" r="10" />
                <circle cx="50" cy="20" r="6" />
                <circle cx="50" cy="80" r="6" />
                <circle cx="20" cy="50" r="6" />
                <circle cx="80" cy="50" r="6" />
              </svg>
            </div>
          </div>
          <div>
            <h3 className="text-cyan-300 font-cyber text-sm">NEXUS</h3>
            <p className="text-cyan-500/70 text-xs">Guardian of Digital Realms</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white p-1 rounded-full hover:bg-gray-700/50 transition-colors"
          aria-label="Close Nexus Chat"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      {/* Messages */}
      <div className="p-4 max-h-[60vh] overflow-y-auto scrollbar-thin scrollbar-thumb-cyan-900 scrollbar-track-transparent">
        {messages.map(message => (
          <div key={message.id} className="flex items-start mb-4">
            {!message.isUser && (
              <NexusVoicePlayer text={message.content} />
            )}
            <div className="flex-1 ml-2">
              <NexusMessage
                content={message.content}
                isUser={message.isUser}
                timestamp={message.timestamp}
              />
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center">
              <div className="w-6 h-6 text-cyan-300 animate-pulse">
                <svg viewBox="0 0 100 100" fill="currentColor">
                  <circle cx="50" cy="50" r="45" fill="none" stroke="currentColor" strokeWidth="6" />
                  <circle cx="50" cy="50" r="10" />
                </svg>
              </div>
            </div>
            <div className="ml-3 bg-gradient-to-r from-cyan-500/20 to-blue-600/10 border border-cyan-400/30 rounded-lg px-4 py-2">
              <div className="flex space-x-2">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-cyan-900/50 bg-black/50">
        <NexusInput onSend={handleSendMessage} disabled={isLoading} />
      </div>
    </div>
  );
}
