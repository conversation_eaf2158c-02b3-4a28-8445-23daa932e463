import React from 'react';

export default function NexusBubble({ onClick }: { onClick: () => void }) {
  return (
    <button
      onClick={onClick}
      className="fixed bottom-6 right-6 z-50 w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-800 rounded-full shadow-lg flex items-center justify-center hover:scale-105 transition-transform border-2 border-cyan-300/30"
      aria-label="Open Nexus Chat"
    >
      <div className="absolute inset-0 rounded-full bg-cyan-500/20 animate-pulse"></div>
      <div className="relative z-10 flex items-center justify-center">
        <svg 
          viewBox="0 0 100 100" 
          className="w-10 h-10 text-cyan-100"
          fill="currentColor"
        >
          <path d="M50 5C25.1 5 5 25.1 5 50s20.1 45 45 45 45-20.1 45-45S74.9 5 50 5zm0 80c-19.3 0-35-15.7-35-35s15.7-35 35-35 35 15.7 35 35-15.7 35-35 35z"/>
          <circle cx="50" cy="50" r="10" />
          <path d="M50 25c-2.8 0-5-2.2-5-5s2.2-5 5-5 5 2.2 5 5-2.2 5-5 5zM50 85c-2.8 0-5-2.2-5-5s2.2-5 5-5 5 2.2 5 5-2.2 5-5 5zM25 50c0-2.8-2.2-5-5-5s-5 2.2-5 5 2.2 5 5 5 5-2.2 5-5zM85 50c0-2.8-2.2-5-5-5s-5 2.2-5 5 2.2 5 5 5 5-2.2 5-5z"/>
          <path d="M32.9 32.9c-2-2-5.1-2-7.1 0-2 2-2 5.1 0 7.1 2 2 5.1 2 7.1 0 2-2 2-5.2 0-7.1zM74.1 74.1c-2-2-5.1-2-7.1 0-2 2-2 5.1 0 7.1 2 2 5.1 2 7.1 0 2-2 2-5.1 0-7.1zM32.9 74.1c-2-2-5.1-2-7.1 0-2 2-2 5.1 0 7.1 2 2 5.1 2 7.1 0 2-2 2-5.1 0-7.1zM74.1 32.9c-2-2-5.1-2-7.1 0-2 2-2 5.1 0 7.1 2 2 5.1 2 7.1 0 2-2 2-5.1 0-7.1z"/>
        </svg>
      </div>
      <div className="absolute inset-0 rounded-full shadow-[0_0_15px_rgba(6,182,212,0.5)] animate-pulse-glow"></div>
    </button>
  );
}
