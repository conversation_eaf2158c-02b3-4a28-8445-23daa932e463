import React from 'react';
import { motion } from 'framer-motion';
import { MessageCircle, Flame, Zap } from 'lucide-react';

export default function NexusBubble({ onClick }: { onClick: () => void }) {
  return (
    <motion.button
      onClick={onClick}
      className="fixed bottom-6 right-6 z-50 w-16 h-16 bg-gradient-to-br from-flame-orange via-neon-cyan to-deep-purple rounded-full shadow-2xl flex items-center justify-center border-2 border-flame-orange/50 hover:border-neon-cyan/70 transition-all duration-300"
      aria-label="Open Nexus Chat - Guardian of Sacred Scrolls"
      whileHover={{
        scale: 1.1,
        rotate: 5,
        boxShadow: "0 0 30px rgba(255, 107, 53, 0.6), 0 0 60px rgba(0, 212, 255, 0.4)"
      }}
      whileTap={{ scale: 0.95 }}
      animate={{
        boxShadow: [
          "0 0 20px rgba(255, 107, 53, 0.4)",
          "0 0 30px rgba(0, 212, 255, 0.6)",
          "0 0 20px rgba(139, 92, 246, 0.4)",
          "0 0 30px rgba(255, 107, 53, 0.6)"
        ]
      }}
      transition={{
        boxShadow: {
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }
      }}
    >
      {/* Animated background pulse */}
      <motion.div
        className="absolute inset-0 rounded-full bg-gradient-to-br from-flame-orange/30 via-neon-cyan/30 to-deep-purple/30"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.6, 0.3]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Main icon container */}
      <div className="relative z-10 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          className="relative"
        >
          {/* Sacred Nexus Symbol */}
          <svg
            viewBox="0 0 100 100"
            className="w-8 h-8 text-white drop-shadow-lg"
            fill="currentColor"
          >
            {/* Central core */}
            <circle cx="50" cy="50" r="8" className="fill-flame-orange" />

            {/* Outer ring */}
            <path d="M50 10C27.9 10 10 27.9 10 50s17.9 40 40 40 40-17.9 40-40S72.1 10 50 10zm0 75c-19.3 0-35-15.7-35-35s15.7-35 35-35 35 15.7 35 35-15.7 35-35 35z"
                  className="fill-neon-cyan" opacity="0.8"/>

            {/* Sacred nodes */}
            <circle cx="50" cy="20" r="3" className="fill-white" />
            <circle cx="50" cy="80" r="3" className="fill-white" />
            <circle cx="20" cy="50" r="3" className="fill-white" />
            <circle cx="80" cy="50" r="3" className="fill-white" />

            {/* Diagonal nodes */}
            <circle cx="35" cy="35" r="2" className="fill-deep-purple" />
            <circle cx="65" cy="65" r="2" className="fill-deep-purple" />
            <circle cx="35" cy="65" r="2" className="fill-deep-purple" />
            <circle cx="65" cy="35" r="2" className="fill-deep-purple" />

            {/* Connection lines */}
            <path d="M50 20 L50 42 M50 58 L50 80 M20 50 L42 50 M58 50 L80 50"
                  stroke="currentColor" strokeWidth="1" opacity="0.6"/>
          </svg>
        </motion.div>

        {/* Floating flame icon */}
        <motion.div
          className="absolute -top-1 -right-1"
          animate={{
            y: [-2, 2, -2],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Flame className="w-3 h-3 text-flame-orange drop-shadow-sm" />
        </motion.div>

        {/* Floating zap icon */}
        <motion.div
          className="absolute -bottom-1 -left-1"
          animate={{
            x: [-1, 1, -1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.3
          }}
        >
          <Zap className="w-3 h-3 text-neon-cyan drop-shadow-sm" />
        </motion.div>
      </div>

      {/* Outer glow ring */}
      <motion.div
        className="absolute inset-0 rounded-full border border-flame-orange/30"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.5, 0.8, 0.5]
        }}
        transition={{
          duration: 2.5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
      />
    </motion.button>
  );
}
