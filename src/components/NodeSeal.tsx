
import { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface NodeSealProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}

const NodeSeal = ({ className, size = 'md', animated = true }: NodeSealProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const sizeMap = {
    sm: { width: 100, height: 100 },
    md: { width: 200, height: 200 },
    lg: { width: 300, height: 300 },
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = sizeMap[size];
    canvas.width = width;
    canvas.height = height;

    const centerX = width / 2;
    const centerY = height / 2;
    
    let outerRingAngle = 0;
    let innerRingAngle = 0;
    let pulseScale = 1;
    let pulseDirection = 0.005;

    const drawSeal = () => {
      // Clear canvas
      ctx.clearRect(0, 0, width, height);
      
      // Draw outer ring
      ctx.beginPath();
      ctx.arc(centerX, centerY, width * 0.45, 0, Math.PI * 2);
      ctx.strokeStyle = 'rgba(0, 255, 255, 0.7)';
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // Draw inner ring
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(innerRingAngle);
      ctx.translate(-centerX, -centerY);
      ctx.beginPath();
      ctx.arc(centerX, centerY, width * 0.35, 0, Math.PI * 2);
      ctx.strokeStyle = 'rgba(138, 43, 226, 0.7)';
      ctx.stroke();
      
      // Add runes/symbols on inner ring
      for (let i = 0; i < 8; i++) {
        const runeAngle = (i / 8) * Math.PI * 2;
        const runeX = centerX + Math.cos(runeAngle) * (width * 0.35);
        const runeY = centerY + Math.sin(runeAngle) * (width * 0.35);
        
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fillRect(runeX - 2, runeY - 2, 4, 4);
      }
      ctx.restore();
      
      // Draw outer decoration
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(-outerRingAngle * 0.5);
      ctx.translate(-centerX, -centerY);
      for (let i = 0; i < 12; i++) {
        const spokeAngle = (i / 12) * Math.PI * 2;
        const innerX = centerX + Math.cos(spokeAngle) * (width * 0.45);
        const innerY = centerY + Math.sin(spokeAngle) * (width * 0.45);
        const outerX = centerX + Math.cos(spokeAngle) * (width * 0.48);
        const outerY = centerY + Math.sin(spokeAngle) * (width * 0.48);
        
        ctx.beginPath();
        ctx.moveTo(innerX, innerY);
        ctx.lineTo(outerX, outerY);
        ctx.strokeStyle = i % 3 === 0 ? 'rgba(255, 69, 0, 0.8)' : 'rgba(0, 255, 255, 0.5)';
        ctx.lineWidth = i % 4 === 0 ? 3 : 1;
        ctx.stroke();
      }
      ctx.restore();
      
      // Draw core
      pulseScale += pulseDirection;
      if (pulseScale > 1.1 || pulseScale < 0.9) {
        pulseDirection *= -1;
      }
      
      const gradient = ctx.createRadialGradient(
        centerX, centerY, 0,
        centerX, centerY, width * 0.15 * pulseScale
      );
      gradient.addColorStop(0, 'rgba(255, 69, 0, 0.9)');
      gradient.addColorStop(0.7, 'rgba(255, 69, 0, 0.6)');
      gradient.addColorStop(1, 'rgba(255, 69, 0, 0)');
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, width * 0.15 * pulseScale, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // Add inner glow
      const innerGlow = ctx.createRadialGradient(
        centerX, centerY, width * 0.05,
        centerX, centerY, width * 0.25
      );
      innerGlow.addColorStop(0, 'rgba(255, 255, 255, 0.2)');
      innerGlow.addColorStop(1, 'rgba(0, 255, 255, 0)');
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, width * 0.25, 0, Math.PI * 2);
      ctx.fillStyle = innerGlow;
      ctx.fill();
      
      if (animated) {
        outerRingAngle += 0.005;
        innerRingAngle -= 0.007;
        requestAnimationFrame(drawSeal);
      }
    };

    drawSeal();
    
    return () => {
      // Cleanup if necessary
    };
  }, [size, animated]);

  return (
    <div className={cn('relative', className)}>
      <canvas 
        ref={canvasRef} 
        className="w-full h-full"
        width={sizeMap[size].width} 
        height={sizeMap[size].height}
      />
    </div>
  );
};

export default NodeSeal;
