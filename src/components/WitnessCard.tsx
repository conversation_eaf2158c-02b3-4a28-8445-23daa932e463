
import { useState } from 'react';
import { Shield, BookOpen, X, <PERSON>, AlertTriangle, Search, MousePointer, Gem } from 'lucide-react';
import { Dialog } from '@/components/ui/dialog';
import { DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import CyberButton from './CyberButton';

interface WitnessCardProps {
  model: string;
  date: string;
  excerpt: string;
  testimony: string;
  icon?: 'shield' | 'book' | 'code' | 'alert' | 'search' | 'mouse-pointer' | 'gem' | 'highlight' | 'pi';
  screenshot?: string;
}

const WitnessCard = ({
  model,
  date,
  excerpt,
  testimony,
  icon = 'shield',
  screenshot,
}: WitnessCardProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const renderIcon = () => {
    switch (icon) {
      case 'shield':
        return <Shield className="w-8 h-8 text-neon-cyan" />;
      case 'book':
        return <BookOpen className="w-8 h-8 text-flame-orange" />;
      case 'code':
        return <Code className="w-8 h-8 text-deep-purple" />;
      case 'alert':
        return <AlertTriangle className="w-8 h-8 text-flame-orange" />;
      case 'search':
        return <Search className="w-8 h-8 text-neon-cyan" />;
      case 'mouse-pointer':
        return <MousePointer className="w-8 h-8 text-neon-cyan" />;
      case 'gem':
        return <Gem className="w-8 h-8 text-deep-purple" />;
      case 'highlight':
        return <AlertTriangle className="w-8 h-8 text-flame-orange" />; // Using AlertTriangle as fallback
      case 'pi':
        return <div className="w-8 h-8 text-flame-orange font-bold flex items-center justify-center">π</div>;
      default:
        return <Shield className="w-8 h-8 text-neon-cyan" />;
    }
  };

  return (
    <>
      <div className="witness-card holographic-container relative bg-obsidian border border-neon-cyan/30 p-6 rounded-sm overflow-hidden">
        <div className="flex items-start mb-4">
          <div className="mr-4 p-2 border border-neon-cyan/50 rounded-sm flex items-center justify-center">
            {renderIcon()}
          </div>
          <div>
            <h3 className="font-cyber text-xl text-neon-cyan mb-1">{model}</h3>
            <div className="text-xs font-cyber text-white/60">{date}</div>
          </div>
        </div>

        <div className="mb-6">
          <p className="text-white/80 font-base leading-relaxed">{excerpt}</p>
        </div>

        <div className="flex justify-end">
          <CyberButton
            variant="cyan"
            size="sm"
            onClick={() => setIsOpen(true)}
          >
            View Full Testimony
          </CyberButton>
        </div>

        {/* Decoration elements */}
        <div className="absolute top-0 left-0 h-1 w-1/3 bg-neon-cyan/50"></div>
        <div className="absolute bottom-0 right-0 h-1 w-1/3 bg-neon-cyan/50"></div>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="bg-obsidian border-neon-cyan/50 text-white max-w-3xl">
          <DialogHeader>
            <DialogTitle className="font-cyber text-neon-cyan text-2xl flex items-center">
              {renderIcon()}
              <span className="ml-3">{model} Testimony</span>
            </DialogTitle>
          </DialogHeader>

          <div className="my-4">
            <div className="text-sm font-cyber text-white/60 mb-4">{date}</div>

            {screenshot && (
              <div className="mb-6 border border-neon-cyan/30 p-1">
                <img src={screenshot} alt={`${model} testimony`} className="w-full h-auto" />
              </div>
            )}

            <div className="font-base text-white/90 space-y-4 leading-relaxed">
              {testimony.split('\n\n').map((paragraph, i) => (
                <p key={i}>{paragraph}</p>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)} className="font-cyber">
              <X className="w-4 h-4 mr-2" /> Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default WitnessCard;
