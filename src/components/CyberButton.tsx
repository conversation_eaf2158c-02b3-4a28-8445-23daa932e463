
import { forwardRef } from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface CyberButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'cyan' | 'orange' | 'purple';
  size?: 'sm' | 'md' | 'lg';
  to?: string;
  fullWidth?: boolean;
}

const CyberButton = forwardRef<HTMLButtonElement, CyberButtonProps>(
  ({ className, variant = 'cyan', size = 'md', to, fullWidth = false, children, ...props }, ref) => {
    const buttonClasses = cn(
      'relative font-cyber tracking-wide uppercase transition-all duration-300 border',
      'inline-flex items-center justify-center overflow-hidden',
      'transform-gpu will-change-transform antialiased select-none',
      'text-rendering-optimizeLegibility',
      '-webkit-font-smoothing-antialiased',
      '-moz-osx-font-smoothing-grayscale',
      'hover:scale-[1.02] active:scale-[0.98]',
      {
        // Variants
        'border-neon-cyan text-neon-cyan hover:bg-neon-cyan/10 neon-border': variant === 'cyan',
        'border-flame-orange text-flame-orange hover:bg-flame-orange/10 flame-border': variant === 'orange',
        'border-deep-purple text-deep-purple hover:bg-deep-purple/10 purple-border': variant === 'purple',
        
        // Sizes
        'text-xs px-3 py-1': size === 'sm',
        'text-sm px-5 py-2': size === 'md',
        'text-base px-7 py-3': size === 'lg',
        
        // Full width
        'w-full': fullWidth,
      },
      className
    );
    
    // If we have a "to" prop, render a Link
    if (to) {
      return (
        <Link to={to} className={buttonClasses}>
          <span className="relative z-10">{children}</span>
          <span className="absolute inset-0 opacity-25 bg-gradient-to-b from-white/5 to-transparent"></span>
          <span className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-current to-transparent"></span>
        </Link>
      );
    }
    
    // Otherwise, render a button
    return (
      <button ref={ref} className={buttonClasses} {...props}>
        <span className="relative z-10">{children}</span>
        <span className="absolute inset-0 opacity-25 bg-gradient-to-b from-white/5 to-transparent"></span>
        <span className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-current to-transparent"></span>
      </button>
    );
  }
);

CyberButton.displayName = 'CyberButton';

export default CyberButton;
