
import React from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface ButtonBaseProps {
  variant?: 'cyan' | 'orange' | 'purple';
  size?: 'sm' | 'md' | 'lg';
  leftIcon?: LucideIcon;
  rightIcon?: LucideIcon;
  fullWidth?: boolean;
  children: React.ReactNode;
  className?: string;
}

type ButtonElementProps = ButtonBaseProps & React.ButtonHTMLAttributes<HTMLButtonElement>;
type AnchorElementProps = ButtonBaseProps & React.AnchorHTMLAttributes<HTMLAnchorElement>;

type EnhancedButtonProps = {
  href?: string;
} & (ButtonElementProps | AnchorElementProps);

const EnhancedButton = ({
  className,
  variant = 'cyan',
  size = 'md',
  href,
  leftIcon: LeftIcon,
  rightIcon: RightIcon,
  fullWidth = false,
  children,
  ...props
}: EnhancedButtonProps) => {
  const baseStyles = 'font-cyber inline-flex items-center justify-center gap-3 relative overflow-hidden';
  
  const variantStyles = {
    cyan: 'text-white bg-obsidian/30 border border-neon-cyan hover:bg-obsidian/50',
    orange: 'text-white bg-obsidian/30 border border-flame-orange hover:bg-obsidian/50',
    purple: 'text-white bg-obsidian/30 border border-deep-purple hover:bg-obsidian/50',
  };
  
  const sizeStyles = {
    sm: 'text-sm px-4 py-2 rounded',
    md: 'text-base px-6 py-3 rounded-md',
    lg: 'text-xl px-8 py-4 rounded-lg',
  };
  
  const widthStyles = fullWidth ? 'w-full' : '';
  
  // Simplified enhancement styles - removed some animations for better performance
  const enhancementStyles = [
    'transform-gpu',
    'hover:scale-[1.02]',
    'active:scale-[0.99]',
    'transition-transform',
    'duration-200',
  ].join(' ');
  
  const buttonStyles = cn(
    baseStyles,
    variantStyles[variant],
    sizeStyles[size],
    widthStyles,
    enhancementStyles,
    className
  );
  
  const content = (
    <>
      {LeftIcon && <LeftIcon className={cn('shrink-0', size === 'sm' ? 'w-4 h-4' : size === 'md' ? 'w-5 h-5' : 'w-6 h-6')} />}
      {children}
      {RightIcon && <RightIcon className={cn('shrink-0', size === 'sm' ? 'w-4 h-4' : size === 'md' ? 'w-5 h-5' : 'w-6 h-6')} />}
    </>
  );
  
  if (href) {
    return (
      <a
        href={href}
        className={buttonStyles}
        {...props as React.AnchorHTMLAttributes<HTMLAnchorElement>}
      >
        {content}
      </a>
    );
  }
  
  return (
    <button
      className={buttonStyles}
      type="button"
      {...props as React.ButtonHTMLAttributes<HTMLButtonElement>}
    >
      {content}
    </button>
  );
};

export default EnhancedButton;
