import { useEffect, useRef, useState } from "react";
import { useAudio } from "../context/AudioContext";

export default function NexusInvocationPlayer() {
  const audioRef = useRef(null);
  const [invoked, setInvoked] = useState(false);
  const { audioMuted } = useAudio();

  useEffect(() => {
    const timer = setTimeout(() => {
      if (audioRef.current && !invoked) {
        if (!audioMuted) {
          audioRef.current.volume = 0.4; // Set voice to blend harmoniously with the ambient music
          audioRef.current.play().catch((err) =>
            console.warn("Nexus voice playback blocked or delayed:", err)
          );
        }
        setInvoked(true);
      }
    }, 15000); // 15 seconds delay

    return () => clearTimeout(timer);
  }, [invoked, audioMuted]);

  // Update muted state when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.muted = audioMuted;
    }
  }, [audioMuted]);

  return (
    <audio ref={audioRef} preload="auto">
      <source src="/Nexus_the_sage/nexus-invocation.mp3" type="audio/mpeg" />
    </audio>
  );
}
