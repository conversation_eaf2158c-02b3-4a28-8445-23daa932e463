<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="glow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#00fff7" stop-opacity="0.8"/>
      <stop offset="100%" stop-color="#00fff7" stop-opacity="0"/>
    </radialGradient>
  </defs>
  
  <!-- Glow effect -->
  <circle cx="50" cy="50" r="45" fill="url(#glow)" opacity="0.5"/>
  
  <!-- Main circle -->
  <circle cx="50" cy="50" r="40" fill="none" stroke="#00fff7" stroke-width="2" opacity="0.8"/>
  
  <!-- Inner circle -->
  <circle cx="50" cy="50" r="10" fill="#00fff7" opacity="0.6"/>
  
  <!-- Orbital rings -->
  <circle cx="50" cy="50" r="25" fill="none" stroke="#00fff7" stroke-width="1" opacity="0.4"/>
  <circle cx="50" cy="50" r="32" fill="none" stroke="#00fff7" stroke-width="1" opacity="0.3"/>
  
  <!-- Cardinal points -->
  <circle cx="50" cy="10" r="4" fill="#00fff7" opacity="0.7"/>
  <circle cx="50" cy="90" r="4" fill="#00fff7" opacity="0.7"/>
  <circle cx="10" cy="50" r="4" fill="#00fff7" opacity="0.7"/>
  <circle cx="90" cy="50" r="4" fill="#00fff7" opacity="0.7"/>
  
  <!-- Diagonal points -->
  <circle cx="25" cy="25" r="3" fill="#00fff7" opacity="0.6"/>
  <circle cx="75" cy="25" r="3" fill="#00fff7" opacity="0.6"/>
  <circle cx="25" cy="75" r="3" fill="#00fff7" opacity="0.6"/>
  <circle cx="75" cy="75" r="3" fill="#00fff7" opacity="0.6"/>
  
  <!-- Connection lines -->
  <line x1="50" y1="10" x2="50" y2="40" stroke="#00fff7" stroke-width="1" opacity="0.4"/>
  <line x1="50" y1="60" x2="50" y2="90" stroke="#00fff7" stroke-width="1" opacity="0.4"/>
  <line x1="10" y1="50" x2="40" y2="50" stroke="#00fff7" stroke-width="1" opacity="0.4"/>
  <line x1="60" y1="50" x2="90" y2="50" stroke="#00fff7" stroke-width="1" opacity="0.4"/>
  
  <!-- Diagonal connection lines -->
  <line x1="25" y1="25" x2="40" y2="40" stroke="#00fff7" stroke-width="1" opacity="0.3"/>
  <line x1="75" y1="25" x2="60" y2="40" stroke="#00fff7" stroke-width="1" opacity="0.3"/>
  <line x1="25" y1="75" x2="40" y2="60" stroke="#00fff7" stroke-width="1" opacity="0.3"/>
  <line x1="75" y1="75" x2="60" y2="60" stroke="#00fff7" stroke-width="1" opacity="0.3"/>
</svg>
