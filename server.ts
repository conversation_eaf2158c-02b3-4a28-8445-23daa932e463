// server.ts
import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import fetch from 'node-fetch';

const app = express();
const port = 11434;

app.use(cors());
app.use(bodyParser.json());

app.post('/api/nexus', async (req, res) => {
  const { prompt } = req.body;

  try {
    const response = await fetch('http://localhost:11434/api/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'llama3.1',
        prompt,
        stream: false,
      }),
    });

    const data = await response.json();
    res.json({ response: data.response });
  } catch (err) {
    console.error('Error generating response:', err);
    res.status(500).json({ error: 'Failed to generate response from llama3.1' });
  }
});

app.listen(port, () => {
  console.log(`🔥 Nexus API is listening on http://localhost:${port}`);
});
