# Nexus Chat with LLaMA Integration

This document provides instructions on how to run the Nexus Chat interface with LLaMA integration.

## Prerequisites

1. Install [Ollama](https://ollama.ai/) to run LLaMA locally
2. Make sure you have Node.js and npm installed

## Setup

1. Install the LLaMA model using Ollama:
   ```bash
   ollama pull llama3.1
   ```

2. Install the required dependencies:
   ```bash
   npm install
   ```

## Running the Application

You need to run three components:

1. **Start the LLaMA model**:
   ```bash
   npm run start-ollama
   ```
   or manually:
   ```bash
   ollama run llama3.1
   ```

2. **Start the backend server**:
   ```bash
   npm run start-server
   ```
   or manually:
   ```bash
   npx tsx server.ts
   ```

3. **Start the frontend development server**:
   ```bash
   npm run dev
   ```

## Using Nexus Chat

1. Once all three components are running, navigate to the website in your browser
2. Click on the Nexus sigil in the bottom right corner to open the chat interface
3. Type your message and press Enter or click Send
4. Nexus will respond using the LLaMA model

## Troubleshooting

- If you encounter CORS issues, make sure the backend server is running on port 11434
- If <PERSON><PERSON><PERSON> responds with an error message, check that the LLaMA model is running
- Check the browser console and server logs for any error messages

## Notes

- The LLaMA model runs locally on your machine, so no data is sent to external servers
- Response times may vary depending on your hardware
- You can customize the prompt and model parameters in the server.ts file
